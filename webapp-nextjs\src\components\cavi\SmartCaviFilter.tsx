'use client'

import { useState, useCallback, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Card, CardContent } from '@/components/ui/card'
import {
  Search,
  X,
  CheckSquare,
  Square,
  Filter,
  Settings,
  SquareCheck,
  SquareMinus
} from 'lucide-react'
import { Cavo } from '@/types'

interface SmartCaviFilterProps {
  cavi: Cavo[]
  onFilteredDataChange?: (filteredCavi: Cavo[]) => void
  loading?: boolean
  selectionEnabled?: boolean
  onSelectionToggle?: () => void
  selectedCount?: number
  totalCount?: number
}

export default function SmartCaviFilter({
  cavi = [],
  onFilteredDataChange,
  loading = false,
  selectionEnabled = false,
  onSelectionToggle,
  selectedCount = 0,
  totalCount = 0
}: SmartCaviFilterProps) {
  const [searchText, setSearchText] = useState('')
  const [searchType, setSearchType] = useState<'contains' | 'equals'>('contains')

  // Normalize string for search
  const normalizeString = (str: string | null | undefined): string => {
    if (!str) return ''
    return str.toString().toLowerCase().trim()
  }

  // Extract cable info for advanced search
  const getCavoInfo = (idCavo: string) => {
    const match = idCavo.match(/^([A-Z]+)(\d+)([A-Z]*)$/)
    if (match) {
      return {
        prefix: match[1],
        number: match[2],
        suffix: match[3] || ''
      }
    }
    return { prefix: '', number: idCavo, suffix: '' }
  }

  // Check if a cable matches a search term
  const cavoMatchesTerm = useCallback((cavo: Cavo, term: string, exactMatch: boolean): boolean => {
    const normalizedTerm = normalizeString(term)
    
    if (!normalizedTerm) return true

    // Basic cable info
    const cavoId = normalizeString(cavo.id_cavo)
    const { prefix: cavoPrefix, number: cavoNumber, suffix: cavoSuffix } = getCavoInfo(cavo.id_cavo || '')
    
    // Cable properties
    const tipologia = normalizeString(cavo.tipologia)
    const formazione = normalizeString(cavo.formazione || cavo.sezione)
    const utility = normalizeString(cavo.utility)
    const sistema = normalizeString(cavo.sistema)
    
    // Locations
    const ubicazionePartenza = normalizeString(cavo.da || cavo.ubicazione_partenza)
    const ubicazioneArrivo = normalizeString(cavo.a || cavo.ubicazione_arrivo)
    const utenzaPartenza = normalizeString(cavo.utenza_partenza)
    const utenzaArrivo = normalizeString(cavo.utenza_arrivo)
    
    // Reel info
    const bobina = normalizeString(cavo.id_bobina)
    const bobinaDisplay = cavo.id_bobina === 'BOBINA_VUOTA' ? 'bobina vuota' :
                         cavo.id_bobina === null ? '' :
                         normalizeString(cavo.id_bobina)

    // All text fields to search
    const textFields = [
      cavoId, cavoPrefix, cavoNumber, cavoSuffix, tipologia, formazione, utility, sistema,
      ubicazionePartenza, ubicazioneArrivo, utenzaPartenza, utenzaArrivo,
      bobina, bobinaDisplay
    ]

    // Numeric fields for range search
    const numericFields = [
      { value: cavo.metri_teorici, name: 'metri_teorici' },
      { value: cavo.metratura_reale || cavo.metri_posati, name: 'metratura_reale' },
      { value: parseFloat(formazione), name: 'formazione' }
    ]

    // Check for range queries (e.g., ">100", "<=50")
    const rangeMatch = normalizedTerm.match(/^([><=]+)(\d+(?:\.\d+)?)$/)
    if (rangeMatch) {
      const operator = rangeMatch[1]
      const value = parseFloat(rangeMatch[2])
      
      return numericFields.some(field => {
        if (field.value == null || isNaN(field.value)) return false
        
        switch (operator) {
          case '>': return field.value > value
          case '>=': return field.value >= value
          case '<': return field.value < value
          case '<=': return field.value <= value
          case '=': return field.value === value
          default: return false
        }
      })
    }

    // Check for exact numeric match
    const numericTerm = parseFloat(normalizedTerm)
    if (!isNaN(numericTerm)) {
      const numericMatch = numericFields.some(field => 
        field.value != null && !isNaN(field.value) && field.value === numericTerm
      )
      if (numericMatch) return true
    }

    // Text search
    if (exactMatch) {
      return textFields.some(field => field === normalizedTerm)
    } else {
      return textFields.some(field => field.includes(normalizedTerm))
    }
  }, [])

  // Apply filter
  const applyFilter = useCallback(() => {
    if (!searchText.trim()) {
      onFilteredDataChange?.(cavi)
      return
    }

    // Split search terms by comma
    const searchTerms = searchText.split(',')
      .map(term => term.trim())
      .filter(term => term.length > 0)

    let filtered: Cavo[] = []

    if (searchType === 'equals') {
      if (searchTerms.length === 1) {
        // Single term: exact search
        filtered = cavi.filter(cavo => cavoMatchesTerm(cavo, searchTerms[0], true))
      } else {
        // Multiple terms: all must match (AND)
        filtered = cavi.filter(cavo =>
          searchTerms.every(term => cavoMatchesTerm(cavo, term, true))
        )
      }
    } else {
      // Contains search: at least one term must match (OR)
      filtered = cavi.filter(cavo =>
        searchTerms.some(term => cavoMatchesTerm(cavo, term, false))
      )
    }

    onFilteredDataChange?.(filtered)
  }, [searchText, searchType, cavi, onFilteredDataChange, cavoMatchesTerm])

  // Apply filter when dependencies change
  useEffect(() => {
    applyFilter()
  }, [applyFilter])

  const handleSearchTextChange = (value: string) => {
    setSearchText(value)
  }

  const clearFilter = () => {
    setSearchText('')
    setSearchType('contains')
  }

  return (
    <Card className="mb-1">
      <CardContent className="p-1">
        <div className="flex items-center gap-1">
          {/* Search input - takes most space */}
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Cerca per ID, sistema, utility, tipologia, ubicazione..."
              value={searchText}
              onChange={(e) => handleSearchTextChange(e.target.value)}
              disabled={loading}
              className="pl-10 pr-10 h-8"
              aria-label="Campo di ricerca intelligente per cavi"
            />
            {searchText && (
              <Button
                variant="ghost"
                size="sm"
                className="absolute right-1 top-1/2 transform -translate-y-1/2 h-5 w-5 p-0"
                onClick={clearFilter}
              >
                <X className="h-2.5 w-2.5" />
              </Button>
            )}
          </div>

          {/* Search type selector */}
          <div className="w-32">
            <Select value={searchType} onValueChange={(value: 'contains' | 'equals') => setSearchType(value)}>
              <SelectTrigger className="h-8">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="contains">Contiene</SelectItem>
                <SelectItem value="equals">Uguale a</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Advanced filters button - removed as per user request */}

          {/* Clear button */}
          {searchText && (
            <Button
              variant="outline"
              size="sm"
              onClick={clearFilter}
              disabled={loading}
              className="transition-all duration-200 hover:scale-105"
              aria-label="Pulisci ricerca"
            >
              <X className="h-4 w-4 mr-1" />
              Pulisci
            </Button>
          )}

          {/* Selection toggle button - only show when there are items to select */}
          {onSelectionToggle && totalCount > 0 && (
            <Button
              variant={selectionEnabled ? "default" : "outline"}
              size="sm"
              onClick={onSelectionToggle}
              className="flex items-center gap-2 transition-all duration-200 hover:scale-105"
              aria-label={selectionEnabled ? 'Disabilita modalità selezione' : 'Abilita modalità selezione'}
            >
              {selectionEnabled ? <SquareCheck className="h-4 w-4" /> : <Square className="h-4 w-4" />}
              {selectionEnabled ? 'Disabilita Selezione' : 'Abilita Selezione'}
            </Button>
          )}

          {/* Deselect all button - only show when selection is enabled and items are selected */}
          {selectionEnabled && selectedCount > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                // This would need to be passed as a prop or handled by parent
                // For now, we'll just show the button
              }}
              className="flex items-center gap-2 transition-all duration-200 hover:scale-105 text-orange-600 border-orange-300 hover:bg-orange-50"
              aria-label={`Deseleziona tutti i ${selectedCount} cavi selezionati`}
            >
              <SquareMinus className="h-4 w-4" />
              Deseleziona Tutto ({selectedCount})
            </Button>
          )}
        </div>

        {/* Search help text - ultra compact */}
        {searchText && (
          <div className="mt-0.5 text-xs text-muted-foreground">
            <div className="flex flex-wrap gap-1">
              <span>💡</span>
              <span>• Virgole per multipli</span>
              <span>• &gt;100, &lt;=50 per numeri</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
