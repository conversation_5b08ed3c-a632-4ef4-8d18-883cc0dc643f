(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6628],{30285:(e,r,t)=>{"use strict";t.d(r,{$:()=>d});var s=t(95155);t(12115);var a=t(99708),n=t(74466),i=t(59434);let c=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:r,variant:t,size:n,asChild:d=!1,...o}=e,u=d?a.DX:"button";return(0,s.jsx)(u,{"data-slot":"button",className:(0,i.cn)(c({variant:t,size:n,className:r})),...o})}},35169:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},35695:(e,r,t)=>{"use strict";var s=t(18999);t.o(s,"useParams")&&t.d(r,{useParams:function(){return s.useParams}}),t.o(s,"usePathname")&&t.d(r,{usePathname:function(){return s.usePathname}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}}),t.o(s,"useSearchParams")&&t.d(r,{useSearchParams:function(){return s.useSearchParams}})},48102:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>m});var s=t(95155),a=t(12115),n=t(35695),i=t(30285),c=t(40283),d=t(25731),o=t(51154),u=t(85339),l=t(35169);function m(){let{user:e,isAuthenticated:r,isLoading:t}=(0,c.A)(),m=(0,n.useRouter)(),h=parseInt((0,n.useParams)().id),[v,x]=(0,a.useState)(null),[g,f]=(0,a.useState)(!0),[p,b]=(0,a.useState)("");(0,a.useEffect)(()=>{t||r||m.push("/login")},[r,t,m]),(0,a.useEffect)(()=>{r&&h&&y()},[r,h]);let y=async()=>{try{f(!0);let e=await d._I.getCantiere(h);x(e),localStorage.setItem("selectedCantiereId",h.toString()),localStorage.setItem("selectedCantiereName",e.commessa)}catch(e){b("Errore nel caricamento del cantiere")}finally{f(!1)}};return((0,a.useEffect)(()=>{v&&!g&&m.push("/cavi")},[v,g,m]),t||g)?(0,s.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,s.jsx)(o.A,{className:"h-8 w-8 animate-spin"})}):p||!v?(0,s.jsxs)("div",{className:"max-w-[90%] mx-auto p-6",children:[(0,s.jsx)("div",{className:"mb-4 p-4 border border-red-200 rounded-lg bg-red-50",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(u.A,{className:"h-4 w-4 text-red-600 mr-2"}),(0,s.jsx)("span",{className:"text-red-800",children:p||"Cantiere non trovato"})]})}),(0,s.jsxs)(i.$,{onClick:()=>{m.push("/cantieri")},children:[(0,s.jsx)(l.A,{className:"mr-2 h-4 w-4"}),"Torna alla Lista Cantieri"]})]}):(0,s.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(o.A,{className:"h-8 w-8 animate-spin mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Caricamento gestione cavi..."})]})})}},51018:(e,r,t)=>{Promise.resolve().then(t.bind(t,48102))},51154:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},59434:(e,r,t)=>{"use strict";t.d(r,{cn:()=>n});var s=t(52596),a=t(39688);function n(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.QP)((0,s.$)(r))}},85339:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])}},e=>{var r=r=>e(e.s=r);e.O(0,[3455,3464,283,8441,1684,7358],()=>r(51018)),_N_E=e.O()}]);