(()=>{var e={};e.id=484,e.ids=[484],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8819:(e,a,s)=>{"use strict";s.d(a,{A:()=>i});let i=(0,s(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10281:(e,a,s)=>{Promise.resolve().then(s.bind(s,57842))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13861:(e,a,s)=>{"use strict";s.d(a,{A:()=>i});let i=(0,s(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34729:(e,a,s)=>{"use strict";s.d(a,{T:()=>r});var i=s(60687),t=s(43210),n=s(4780);let r=t.forwardRef(({className:e,...a},s)=>(0,i.jsx)("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:s,...a}));r.displayName="Textarea"},41550:(e,a,s)=>{"use strict";s.d(a,{A:()=>i});let i=(0,s(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},46657:(e,a,s)=>{"use strict";s.d(a,{k:()=>b});var i=s(60687),t=s(43210),n=s(11273),r=s(14163),l="Progress",[o,c]=(0,n.A)(l),[d,m]=o(l),u=t.forwardRef((e,a)=>{var s,t;let{__scopeProgress:n,value:l=null,max:o,getValueLabel:c=p,...m}=e;(o||0===o)&&!g(o)&&console.error((s=`${o}`,`Invalid prop \`max\` of value \`${s}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let u=g(o)?o:100;null===l||f(l,u)||console.error((t=`${l}`,`Invalid prop \`value\` of value \`${t}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let x=f(l,u)?l:null,h=j(x)?c(x,u):void 0;return(0,i.jsx)(d,{scope:n,value:x,max:u,children:(0,i.jsx)(r.sG.div,{"aria-valuemax":u,"aria-valuemin":0,"aria-valuenow":j(x)?x:void 0,"aria-valuetext":h,role:"progressbar","data-state":v(x,u),"data-value":x??void 0,"data-max":u,...m,ref:a})})});u.displayName=l;var x="ProgressIndicator",h=t.forwardRef((e,a)=>{let{__scopeProgress:s,...t}=e,n=m(x,s);return(0,i.jsx)(r.sG.div,{"data-state":v(n.value,n.max),"data-value":n.value??void 0,"data-max":n.max,...t,ref:a})});function p(e,a){return`${Math.round(e/a*100)}%`}function v(e,a){return null==e?"indeterminate":e===a?"complete":"loading"}function j(e){return"number"==typeof e}function g(e){return j(e)&&!isNaN(e)&&e>0}function f(e,a){return j(e)&&!isNaN(e)&&e<=a&&e>=0}h.displayName=x;var N=s(4780);function b({className:e,value:a,...s}){return(0,i.jsx)(u,{"data-slot":"progress",className:(0,N.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",e),...s,children:(0,i.jsx)(h,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:`translateX(-${100-(a||0)}%)`}})})}},52137:(e,a,s)=>{Promise.resolve().then(s.bind(s,83967))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56896:(e,a,s)=>{"use strict";s.d(a,{S:()=>l});var i=s(60687);s(43210);var t=s(40211),n=s(13964),r=s(4780);function l({className:e,...a}){return(0,i.jsx)(t.bL,{"data-slot":"checkbox",className:(0,r.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...a,children:(0,i.jsx)(t.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,i.jsx)(n.A,{className:"size-3.5"})})})}},57842:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>i});let i=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\comande\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\app\\comande\\page.tsx","default")},58869:(e,a,s)=>{"use strict";s.d(a,{A:()=>i});let i=(0,s(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},59316:(e,a,s)=>{"use strict";s.r(a),s.d(a,{GlobalError:()=>r.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>c});var i=s(65239),t=s(48088),n=s(88170),r=s.n(n),l=s(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(a,o);let c={children:["",{children:["comande",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,57842)),"C:\\CMS\\webapp-nextjs\\src\\app\\comande\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\CMS\\webapp-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\CMS\\webapp-nextjs\\src\\app\\comande\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new i.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/comande/page",pathname:"/comande",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,a,s)=>{"use strict";s.d(a,{A:()=>i});let i=(0,s(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},63503:(e,a,s)=>{"use strict";s.d(a,{Cf:()=>m,Es:()=>x,L3:()=>h,c7:()=>u,lG:()=>l,rr:()=>p,zM:()=>o});var i=s(60687);s(43210);var t=s(26134),n=s(11860),r=s(4780);function l({...e}){return(0,i.jsx)(t.bL,{"data-slot":"dialog",...e})}function o({...e}){return(0,i.jsx)(t.l9,{"data-slot":"dialog-trigger",...e})}function c({...e}){return(0,i.jsx)(t.ZL,{"data-slot":"dialog-portal",...e})}function d({className:e,...a}){return(0,i.jsx)(t.hJ,{"data-slot":"dialog-overlay",className:(0,r.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...a})}function m({className:e,children:a,showCloseButton:s=!0,...l}){return(0,i.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,i.jsx)(d,{}),(0,i.jsxs)(t.UC,{"data-slot":"dialog-content",className:(0,r.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...l,children:[a,s&&(0,i.jsxs)(t.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,i.jsx)(n.A,{}),(0,i.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function u({className:e,...a}){return(0,i.jsx)("div",{"data-slot":"dialog-header",className:(0,r.cn)("flex flex-col gap-2 text-center sm:text-left",e),...a})}function x({className:e,...a}){return(0,i.jsx)("div",{"data-slot":"dialog-footer",className:(0,r.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...a})}function h({className:e,...a}){return(0,i.jsx)(t.hE,{"data-slot":"dialog-title",className:(0,r.cn)("text-lg leading-none font-semibold",e),...a})}function p({className:e,...a}){return(0,i.jsx)(t.VY,{"data-slot":"dialog-description",className:(0,r.cn)("text-muted-foreground text-sm",e),...a})}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83967:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>Q});var i=s(60687),t=s(43210),n=s(44493),r=s(29523),l=s(96834),o=s(89667),c=s(15391),d=s(6211),m=s(63213),u=s(62185),x=s(63503),h=s(80013),p=s(34729),v=s(15079),j=s(91821),g=s(6727),f=s(93613),N=s(41862),b=s(58869);let A={POSA:"POSA",COLLEGAMENTO_PARTENZA:"COLLEGAMENTO_PARTENZA",COLLEGAMENTO_ARRIVO:"COLLEGAMENTO_ARRIVO",CERTIFICAZIONE:"CERTIFICAZIONE"},_={INSTALLATO:"Installato"};function C(e,a,s){let i={isValid:!0,errors:[],warnings:[],info:[],caviValidi:[],caviProblematici:[]};return e&&0!==e.length?s&&""!==s.trim()?(e.forEach(e=>{let t=function(e,a,s){let i={id_cavo:e.id_cavo,isValid:!0,errors:[],warnings:[],info:[]},t=function(e,a){let s={errors:[],warnings:[],info:[]},i=e.stato_installazione===_.INSTALLATO,t=e.metratura_reale&&parseFloat(e.metratura_reale)>0,n=e.collegamenti&&parseInt(e.collegamenti)>0,r="CERTIFICATO"===e.stato_certificazione;switch(a){case A.POSA:i&&s.errors.push(`Cavo ${e.id_cavo} \xe8 gi\xe0 installato e non pu\xf2 essere assegnato a comanda POSA`),t&&s.warnings.push(`Cavo ${e.id_cavo} ha gi\xe0 metratura reale registrata`);break;case A.COLLEGAMENTO_PARTENZA:case A.COLLEGAMENTO_ARRIVO:i||t||s.warnings.push(`Cavo ${e.id_cavo} non risulta installato. Verificare prerequisiti.`),n&&s.warnings.push(`Cavo ${e.id_cavo} risulta gi\xe0 collegato`);break;case A.CERTIFICAZIONE:i||s.errors.push(`Cavo ${e.id_cavo} deve essere installato per la certificazione`),n||s.warnings.push(`Cavo ${e.id_cavo} non risulta collegato. Verificare prerequisiti.`),r&&s.warnings.push(`Cavo ${e.id_cavo} \xe8 gi\xe0 certificato`)}return s}(e,a);i.errors.push(...t.errors),i.warnings.push(...t.warnings),i.info.push(...t.info);let n=function(e,a){let s={errors:[],warnings:[],info:[]};switch(a){case A.POSA:e.comanda_posa&&s.errors.push(`Cavo ${e.id_cavo} ha gi\xe0 comanda POSA assegnata: ${e.comanda_posa}`);break;case A.COLLEGAMENTO_PARTENZA:e.comanda_partenza&&s.errors.push(`Cavo ${e.id_cavo} ha gi\xe0 comanda COLLEGAMENTO_PARTENZA assegnata: ${e.comanda_partenza}`);break;case A.COLLEGAMENTO_ARRIVO:e.comanda_arrivo&&s.errors.push(`Cavo ${e.id_cavo} ha gi\xe0 comanda COLLEGAMENTO_ARRIVO assegnata: ${e.comanda_arrivo}`);break;case A.CERTIFICAZIONE:e.comanda_certificazione&&s.errors.push(`Cavo ${e.id_cavo} ha gi\xe0 comanda CERTIFICAZIONE assegnata: ${e.comanda_certificazione}`)}return s}(e,a);i.errors.push(...n.errors),i.warnings.push(...n.warnings),i.info.push(...n.info);let r=function(e,a){let s={warnings:[],info:[]};switch(a){case A.COLLEGAMENTO_PARTENZA:case A.COLLEGAMENTO_ARRIVO:!e.comanda_posa&&(!e.metratura_reale||0>=parseFloat(e.metratura_reale))&&s.warnings.push(`Cavo ${e.id_cavo} non ha comanda posa assegnata e non risulta installato. Verificare prerequisiti.`);break;case A.CERTIFICAZIONE:e.comanda_partenza||e.comanda_arrivo||s.warnings.push(`Cavo ${e.id_cavo} non ha comande di collegamento assegnate. Verificare prerequisiti.`)}return s}(e,a);i.warnings.push(...r.warnings),i.info.push(...r.info);let l=function(e,a,s){let i={warnings:[],info:[]},t=[...new Set(Object.values({posa:e.responsabile_posa||"",partenza:e.responsabile_partenza||"",arrivo:e.responsabile_arrivo||"",certificazione:e.responsabile_certificazione||""}).filter(e=>e&&""!==e.trim()))];return t.length>1&&!t.includes(s)&&i.warnings.push(`Cavo ${e.id_cavo} ha gi\xe0 responsabili diversi (${t.join(", ")}). Nuovo responsabile: ${s}`),i}(e,0,s);return i.warnings.push(...l.warnings),i.info.push(...l.info),i.isValid=0===i.errors.length,i}(e,a,s);i.errors.push(...t.errors),i.warnings.push(...t.warnings),i.info.push(...t.info),t.isValid?i.caviValidi.push(e):i.caviProblematici.push({cavo:e,issues:t.errors})}),i.isValid=0===i.errors.length):(i.errors.push("Responsabile non specificato"),i.isValid=!1):(i.errors.push("Nessun cavo selezionato per la comanda"),i.isValid=!1),i}function y(e){let a=[];return e.errors.length>0&&(a.push(`❌ Errori (${e.errors.length}):`),e.errors.forEach(e=>a.push(`  • ${e}`))),e.warnings.length>0&&(a.push(`⚠️ Avvisi (${e.warnings.length}):`),e.warnings.forEach(e=>a.push(`  • ${e}`))),e.info.length>0&&(a.push(`ℹ️ Informazioni (${e.info.length}):`),e.info.forEach(e=>a.push(`  • ${e}`))),e.caviValidi.length>0&&a.push(`✅ Cavi validi: ${e.caviValidi.length}`),e.caviProblematici.length>0&&a.push(`❌ Cavi problematici: ${e.caviProblematici.length}`),a.join("\n")}function w({open:e,onClose:a,caviSelezionati:s=[],tipoComanda:n,onSuccess:l,onError:c,onComandaCreated:d}){let[A,_]=(0,t.useState)(!1),[w,E]=(0,t.useState)(""),[O,z]=(0,t.useState)([]),[S,R]=(0,t.useState)(!1),[I,T]=(0,t.useState)(""),[k,$]=(0,t.useState)(!1),{cantiere:L}=(0,m.A)(),[P,M]=(0,t.useState)(0),[V,F]=(0,t.useState)({tipo_comanda:n||"POSA",responsabile:"",descrizione:"",data_scadenza:"",numero_componenti_squadra:1}),G=async()=>{try{let e;if(_(!0),E(""),!V.tipo_comanda)return void E("Seleziona il tipo di comanda");if(!V.responsabile)return void E("Seleziona un responsabile");if(!P||P<=0)return void E("Cantiere non selezionato");if(s.length>0){let e=C(s,V.tipo_comanda,V.responsabile);if(!e.isValid){E("Validazione cavi fallita. Controllare i dettagli nella sezione validazione."),T(y(e)),$(!0);return}e.warnings.length>0&&(T(y(e)),$(!0))}let i={tipo_comanda:V.tipo_comanda,responsabile:V.responsabile,descrizione:V.descrizione||null,data_scadenza:V.data_scadenza||null,numero_componenti_squadra:V.numero_componenti_squadra};e=s&&s.length>0?await u.CV.createComandaWithCavi(P,i,s):await u.CV.createComanda(P,i);let t=e?.data?.codice_comanda||e?.codice_comanda,n=s&&s.length>0?`Comanda ${t} creata con successo per ${s.length} cavi`:`Comanda ${t} creata con successo`;l(n),d?.(),a()}catch(e){c(e.response?.data?.detail||e.message||"Errore durante la creazione della comanda")}finally{_(!1)}};return(0,i.jsx)(x.lG,{open:e,onOpenChange:a,children:(0,i.jsxs)(x.Cf,{className:"sm:max-w-[600px]",children:[(0,i.jsxs)(x.c7,{children:[(0,i.jsxs)(x.L3,{className:"flex items-center gap-2",children:[(0,i.jsx)(g.A,{className:"h-5 w-5"}),"Crea Nuova Comanda"]}),(0,i.jsx)(x.rr,{children:s&&s.length>0?`Crea una comanda per ${s.length} cavi selezionati`:"Crea una nuova comanda di lavoro"})]}),(0,i.jsxs)("div",{className:"space-y-6 py-4",children:[w&&(0,i.jsxs)(j.Fc,{variant:"destructive",children:[(0,i.jsx)(f.A,{className:"h-4 w-4"}),(0,i.jsx)(j.TN,{children:w})]}),s.length>0&&(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("h4",{className:"text-sm font-medium",children:["Validazione Cavi (",s.length," selezionati)"]}),(0,i.jsx)(r.$,{type:"button",variant:"outline",size:"sm",onClick:()=>{if(0===s.length){T("Nessun cavo selezionato per la validazione"),$(!0);return}T(y(C(s,V.tipo_comanda,V.responsabile))),$(!0)},disabled:!V.tipo_comanda||!V.responsabile,children:"Valida Cavi"})]}),k&&I&&(0,i.jsxs)(j.Fc,{children:[(0,i.jsx)(f.A,{className:"h-4 w-4"}),(0,i.jsx)(j.TN,{children:(0,i.jsx)("pre",{className:"whitespace-pre-wrap text-xs font-mono",children:I})})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(h.J,{htmlFor:"tipo",children:"Tipo Comanda *"}),(0,i.jsxs)(v.l6,{value:V.tipo_comanda,onValueChange:e=>F(a=>({...a,tipo_comanda:e})),children:[(0,i.jsx)(v.bq,{children:(0,i.jsx)(v.yv,{})}),(0,i.jsx)(v.gC,{children:[{value:"POSA",label:"\uD83D\uDD27 Posa Cavi",description:"Installazione e posa dei cavi"},{value:"COLLEGAMENTO_PARTENZA",label:"\uD83D\uDD0C Collegamento Partenza",description:"Collegamento lato partenza"},{value:"COLLEGAMENTO_ARRIVO",label:"⚡ Collegamento Arrivo",description:"Collegamento lato arrivo"},{value:"CERTIFICAZIONE",label:"\uD83D\uDCCB Certificazione",description:"Test e certificazione"}].map(e=>(0,i.jsx)(v.eb,{value:e.value,children:(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-medium",children:e.label}),(0,i.jsx)("div",{className:"text-sm text-slate-500",children:e.description})]})},e.value))})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(h.J,{htmlFor:"responsabile",children:"Responsabile *"}),S?(0,i.jsxs)("div",{className:"flex items-center gap-2 p-2 text-sm text-slate-500",children:[(0,i.jsx)(N.A,{className:"h-4 w-4 animate-spin"}),"Caricamento responsabili..."]}):(0,i.jsxs)(v.l6,{value:V.responsabile,onValueChange:e=>F(a=>({...a,responsabile:e})),children:[(0,i.jsx)(v.bq,{children:(0,i.jsx)(v.yv,{placeholder:"Seleziona responsabile"})}),(0,i.jsx)(v.gC,{children:O.map(e=>(0,i.jsx)(v.eb,{value:e.nome_responsabile,children:(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(b.A,{className:"h-4 w-4"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-medium",children:e.nome_responsabile}),e.numero_telefono&&(0,i.jsx)("div",{className:"text-sm text-slate-500",children:e.numero_telefono})]})]})},e.id_responsabile))})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(h.J,{htmlFor:"descrizione",children:"Descrizione"}),(0,i.jsx)(p.T,{id:"descrizione",placeholder:"Descrizione opzionale della comanda...",value:V.descrizione,onChange:e=>F(a=>({...a,descrizione:e.target.value})),rows:3})]}),(0,i.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(h.J,{htmlFor:"data_scadenza",children:"Data Scadenza"}),(0,i.jsx)(o.p,{id:"data_scadenza",type:"date",value:V.data_scadenza,onChange:e=>F(a=>({...a,data_scadenza:e.target.value}))})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(h.J,{htmlFor:"squadra",children:"Componenti Squadra"}),(0,i.jsx)(o.p,{id:"squadra",type:"number",min:"1",max:"20",value:V.numero_componenti_squadra,onChange:e=>F(a=>({...a,numero_componenti_squadra:parseInt(e.target.value)||1}))})]})]}),s&&s.length>0&&(0,i.jsxs)("div",{className:"p-3 bg-blue-50 rounded-lg border border-blue-200",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2 text-blue-700",children:[(0,i.jsx)(g.A,{className:"h-4 w-4"}),(0,i.jsxs)("span",{className:"font-medium",children:["Cavi da assegnare: ",s.length]})]}),(0,i.jsx)("div",{className:"text-sm text-blue-600 mt-1",children:"I cavi selezionati verranno automaticamente assegnati a questa comanda"})]})]}),(0,i.jsxs)(x.Es,{children:[(0,i.jsx)(r.$,{variant:"outline",onClick:a,disabled:A,children:"Annulla"}),(0,i.jsxs)(r.$,{onClick:G,disabled:A,children:[A&&(0,i.jsx)(N.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Crea Comanda"]})]})]})})}var E=s(41312),O=s(96474),z=s(11860),S=s(8819),R=s(62688);let I=(0,R.A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]]);var T=s(41550),k=s(63143),$=s(88233);function L({open:e,onClose:a,onSuccess:s,onError:l}){let[c,d]=(0,t.useState)(!1),[p,v]=(0,t.useState)(""),[g,A]=(0,t.useState)([]),[_,C]=(0,t.useState)(null),[y,w]=(0,t.useState)(!1),{cantiere:R}=(0,m.A)(),[L,P]=(0,t.useState)(0),[M,V]=(0,t.useState)({nome_responsabile:"",numero_telefono:"",mail:""}),F=async()=>{try{d(!0);let e=await u.AR.getResponsabili(L),a=e?.data||e||[];A(Array.isArray(a)?a:[])}catch(e){v("Errore durante il caricamento dei responsabili")}finally{d(!1)}},G=async()=>{try{d(!0),v("");let e={nome_responsabile:M.nome_responsabile.trim(),numero_telefono:M.numero_telefono.trim()||null,mail:M.mail.trim()||null},a=function(e){var a,s;let i=[];return e.nome_responsabile&&e.nome_responsabile.trim()||i.push("Il nome del responsabile \xe8 obbligatorio"),e.mail||e.numero_telefono||i.push("Almeno uno tra email e telefono deve essere specificato"),e.mail&&(a=e.mail,!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(a))&&i.push("Formato email non valido"),e.numero_telefono&&(s=e.numero_telefono,!/^[\+]?[0-9\s\-\(\)]{8,15}$/.test(s.replace(/\s/g,"")))&&i.push("Formato telefono non valido"),{isValid:0===i.length,errors:i}}(e);if(!a.isValid)return void v(`Errori di validazione: ${a.errors.join(", ")}`);await u.AR.createResponsabile(L,e),s("Responsabile aggiunto con successo"),V({nome_responsabile:"",numero_telefono:"",mail:""}),w(!1),F()}catch(e){v(e.response?.data?.detail||"Errore durante la creazione del responsabile")}finally{d(!1)}},q=e=>{V({nome_responsabile:e.nome_responsabile,numero_telefono:e.numero_telefono||"",mail:e.mail||""}),C(e.id_responsabile),w(!1)},Z=async()=>{if(_)try{if(d(!0),v(""),!M.nome_responsabile.trim())return void v("Il nome del responsabile \xe8 obbligatorio");if(M.mail&&!M.mail.includes("@"))return void v("Inserisci un indirizzo email valido");let e={nome_responsabile:M.nome_responsabile.trim(),numero_telefono:M.numero_telefono.trim()||null,mail:M.mail.trim()||null};await u.AR.updateResponsabile(L,_,e),s("Responsabile aggiornato con successo"),V({nome_responsabile:"",numero_telefono:"",mail:""}),C(null),F()}catch(e){v(e.response?.data?.detail||"Errore durante l'aggiornamento del responsabile")}finally{d(!1)}},B=async(e,a)=>{if(confirm(`Sei sicuro di voler eliminare il responsabile "${a}"?`))try{d(!0),await u.AR.deleteResponsabile(L,e),s("Responsabile eliminato con successo"),F()}catch(e){l(e.response?.data?.detail||"Errore durante l'eliminazione del responsabile")}finally{d(!1)}},D=()=>{C(null),w(!1),V({nome_responsabile:"",numero_telefono:"",mail:""}),v("")};return(0,i.jsx)(x.lG,{open:e,onOpenChange:a,children:(0,i.jsxs)(x.Cf,{className:"sm:max-w-[700px] max-h-[80vh] overflow-y-auto",children:[(0,i.jsxs)(x.c7,{children:[(0,i.jsxs)(x.L3,{className:"flex items-center gap-2",children:[(0,i.jsx)(E.A,{className:"h-5 w-5"}),"Gestisci Responsabili"]}),(0,i.jsxs)(x.rr,{children:["Gestisci i responsabili per il cantiere ",L]})]}),(0,i.jsxs)("div",{className:"space-y-6 py-4",children:[p&&(0,i.jsxs)(j.Fc,{variant:"destructive",children:[(0,i.jsx)(f.A,{className:"h-4 w-4"}),(0,i.jsx)(j.TN,{children:p})]}),!y&&!_&&(0,i.jsxs)(r.$,{onClick:()=>w(!0),className:"w-full",variant:"outline",children:[(0,i.jsx)(O.A,{className:"h-4 w-4 mr-2"}),"Aggiungi Nuovo Responsabile"]}),(y||_)&&(0,i.jsx)(n.Zp,{className:"border-2 border-blue-200",children:(0,i.jsxs)(n.Wu,{className:"p-4 space-y-4",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("h4",{className:"font-medium",children:_?"Modifica Responsabile":"Nuovo Responsabile"}),(0,i.jsx)(r.$,{variant:"ghost",size:"sm",onClick:D,children:(0,i.jsx)(z.A,{className:"h-4 w-4"})})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(h.J,{htmlFor:"nome",children:"Nome Responsabile *"}),(0,i.jsx)(o.p,{id:"nome",placeholder:"Nome e cognome",value:M.nome_responsabile,onChange:e=>V(a=>({...a,nome_responsabile:e.target.value}))})]}),(0,i.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(h.J,{htmlFor:"telefono",children:"Numero Telefono"}),(0,i.jsx)(o.p,{id:"telefono",placeholder:"+39 ************",value:M.numero_telefono,onChange:e=>V(a=>({...a,numero_telefono:e.target.value}))})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(h.J,{htmlFor:"email",children:"Email"}),(0,i.jsx)(o.p,{id:"email",type:"email",placeholder:"<EMAIL>",value:M.mail,onChange:e=>V(a=>({...a,mail:e.target.value}))})]})]})]}),(0,i.jsxs)("div",{className:"flex gap-2 pt-2",children:[(0,i.jsxs)(r.$,{onClick:_?Z:G,disabled:c,className:"flex-1",children:[c&&(0,i.jsx)(N.A,{className:"mr-2 h-4 w-4 animate-spin"}),(0,i.jsx)(S.A,{className:"mr-2 h-4 w-4"}),_?"Aggiorna":"Aggiungi"]}),(0,i.jsx)(r.$,{variant:"outline",onClick:D,children:"Annulla"})]})]})}),(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)("h4",{className:"font-medium flex items-center gap-2",children:[(0,i.jsx)(E.A,{className:"h-4 w-4"}),"Responsabili Esistenti (",g.length,")"]}),c&&0===g.length?(0,i.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(N.A,{className:"h-4 w-4 animate-spin"}),"Caricamento responsabili..."]})}):0===g.length?(0,i.jsx)("div",{className:"text-center py-8 text-slate-500",children:"Nessun responsabile trovato"}):(0,i.jsx)("div",{className:"space-y-2",children:g.map(e=>(0,i.jsx)(n.Zp,{className:_===e.id_responsabile?"border-blue-300":"",children:(0,i.jsx)(n.Wu,{className:"p-4",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex-1",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,i.jsx)(b.A,{className:"h-4 w-4 text-slate-500"}),(0,i.jsx)("span",{className:"font-medium",children:e.nome_responsabile})]}),(0,i.jsxs)("div",{className:"space-y-1 text-sm text-slate-600",children:[e.numero_telefono&&(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(I,{className:"h-3 w-3"}),e.numero_telefono]}),e.mail&&(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(T.A,{className:"h-3 w-3"}),e.mail]})]})]}),(0,i.jsxs)("div",{className:"flex gap-2",children:[(0,i.jsx)(r.$,{variant:"ghost",size:"sm",onClick:()=>q(e),disabled:c||_===e.id_responsabile,children:(0,i.jsx)(k.A,{className:"h-4 w-4"})}),(0,i.jsx)(r.$,{variant:"ghost",size:"sm",onClick:()=>B(e.id_responsabile,e.nome_responsabile),disabled:c,className:"text-red-600 hover:text-red-700",children:(0,i.jsx)($.A,{className:"h-4 w-4"})})]})]})})},e.id_responsabile))})]})]}),(0,i.jsx)(x.Es,{children:(0,i.jsx)(r.$,{variant:"outline",onClick:a,children:"Chiudi"})})]})})}var P=s(46657),M=s(58559),V=s(40228),F=s(48730),G=s(5336),q=s(23361);function Z({open:e,onClose:a,codiceComanda:s,onSuccess:o,onError:d}){let[u,h]=(0,t.useState)(!1),[p,v]=(0,t.useState)(""),[A,_]=(0,t.useState)(null),{cantiere:C}=(0,m.A)(),[y,w]=(0,t.useState)(0),O=e=>new Date(e).toLocaleDateString("it-IT",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"});return s?(0,i.jsx)(x.lG,{open:e,onOpenChange:a,children:(0,i.jsxs)(x.Cf,{className:"sm:max-w-[800px] max-h-[90vh] overflow-y-auto",children:[(0,i.jsxs)(x.c7,{children:[(0,i.jsxs)(x.L3,{className:"flex items-center gap-2",children:[(0,i.jsx)(g.A,{className:"h-5 w-5"}),"Dettagli Comanda ",s]}),(0,i.jsx)(x.rr,{children:"Visualizza tutti i dettagli e lo stato della comanda"})]}),(0,i.jsxs)("div",{className:"space-y-6 py-4",children:[p&&(0,i.jsxs)(j.Fc,{variant:"destructive",children:[(0,i.jsx)(f.A,{className:"h-4 w-4"}),(0,i.jsx)(j.TN,{children:p})]}),u?(0,i.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(N.A,{className:"h-5 w-5 animate-spin"}),"Caricamento dettagli comanda..."]})}):A?(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)(n.Zp,{children:[(0,i.jsx)(n.aR,{children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,i.jsx)(M.A,{className:"h-5 w-5"}),"Informazioni Generali"]}),(0,i.jsxs)("div",{className:"flex gap-2",children:[(e=>{let a={POSA:{label:"Posa Cavi",icon:"\uD83D\uDD27"},COLLEGAMENTO_PARTENZA:{label:"Collegamento Partenza",icon:"\uD83D\uDD0C"},COLLEGAMENTO_ARRIVO:{label:"Collegamento Arrivo",icon:"⚡"},CERTIFICAZIONE:{label:"Certificazione",icon:"\uD83D\uDCCB"}}[e]||{label:e,icon:"❓"};return(0,i.jsxs)(l.E,{variant:"outline",className:"bg-blue-50 text-blue-700 border-blue-200",children:[a.icon," ",a.label]})})(A.tipo_comanda),(e=>{let a=(0,c.Fw)(e);return(0,i.jsx)(l.E,{className:a.badge,children:e})})(A.stato)]})]})}),(0,i.jsx)(n.Wu,{className:"space-y-4",children:(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(V.A,{className:"h-4 w-4 text-slate-500"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-slate-500",children:"Data Creazione"}),(0,i.jsx)("p",{className:"font-medium",children:O(A.data_creazione)})]})]}),A.data_scadenza&&(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(F.A,{className:"h-4 w-4 text-slate-500"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-slate-500",children:"Scadenza"}),(0,i.jsx)("p",{className:"font-medium",children:O(A.data_scadenza)})]})]}),A.data_completamento&&(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(G.A,{className:"h-4 w-4 text-green-500"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-slate-500",children:"Completamento"}),(0,i.jsx)("p",{className:"font-medium text-green-700",children:O(A.data_completamento)})]})]})]}),(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(E.A,{className:"h-4 w-4 text-slate-500"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-slate-500",children:"Componenti Squadra"}),(0,i.jsxs)("p",{className:"font-medium",children:[A.numero_componenti_squadra," persone"]})]})]}),A.descrizione&&(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-slate-500",children:"Descrizione"}),(0,i.jsx)("p",{className:"font-medium",children:A.descrizione})]})]})]})})]}),(0,i.jsxs)(n.Zp,{children:[(0,i.jsx)(n.aR,{children:(0,i.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,i.jsx)(b.A,{className:"h-5 w-5"}),"Responsabile"]})}),(0,i.jsx)(n.Wu,{children:(0,i.jsx)("div",{className:"flex items-start gap-4",children:(0,i.jsxs)("div",{className:"flex-1",children:[(0,i.jsx)("p",{className:"font-medium text-lg",children:A.responsabile||"Non assegnato"}),A.responsabile_dettagli&&(0,i.jsxs)("div",{className:"mt-2 space-y-1 text-sm text-slate-600",children:[A.responsabile_dettagli.numero_telefono&&(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(I,{className:"h-3 w-3"}),A.responsabile_dettagli.numero_telefono]}),A.responsabile_dettagli.mail&&(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(T.A,{className:"h-3 w-3"}),A.responsabile_dettagli.mail]})]})]})})})]}),A.progresso&&(0,i.jsxs)(n.Zp,{children:[(0,i.jsx)(n.aR,{children:(0,i.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,i.jsx)(M.A,{className:"h-5 w-5"}),"Progresso Lavori"]})}),(0,i.jsx)(n.Wu,{children:(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("span",{className:"text-sm font-medium",children:"Completamento"}),(0,i.jsxs)("span",{className:"text-sm font-medium",children:[A.progresso.percentuale,"%"]})]}),(0,i.jsx)(P.k,{value:A.progresso.percentuale,className:"h-2"}),(0,i.jsxs)("div",{className:"flex justify-between text-sm text-slate-600",children:[(0,i.jsxs)("span",{children:[A.progresso.completati," completati"]}),(0,i.jsxs)("span",{children:[A.progresso.totale," totali"]})]})]})})]}),A.cavi_assegnati&&A.cavi_assegnati.length>0&&(0,i.jsxs)(n.Zp,{children:[(0,i.jsx)(n.aR,{children:(0,i.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,i.jsx)(q.A,{className:"h-5 w-5"}),"Cavi Assegnati (",A.cavi_assegnati.length,")"]})}),(0,i.jsx)(n.Wu,{children:(0,i.jsx)("div",{className:"space-y-2 max-h-40 overflow-y-auto",children:A.cavi_assegnati.map((e,a)=>(0,i.jsxs)("div",{className:"flex items-center justify-between p-2 bg-slate-50 rounded",children:[(0,i.jsx)("span",{className:"font-mono text-sm",children:e.id_cavo||e}),e.stato&&(0,i.jsx)(l.E,{variant:"outline",className:"text-xs",children:e.stato})]},a))})})]})]}):(0,i.jsx)("div",{className:"text-center py-8 text-slate-500",children:"Nessun dettaglio disponibile"})]}),(0,i.jsxs)(x.Es,{children:[(0,i.jsx)(r.$,{variant:"outline",onClick:a,children:"Chiudi"}),A&&(0,i.jsx)(r.$,{onClick:()=>{o("Funzione di modifica in sviluppo")},children:"Modifica Comanda"})]})]})}):null}var B=s(43649),D=s(29867);function J({open:e,onClose:a,codiceComanda:s,tipoComanda:n,onSuccess:c,onError:d}){let[m,p]=(0,t.useState)([]),[v,j]=(0,t.useState)({}),[g,f]=(0,t.useState)(!1),[b,A]=(0,t.useState)(!1),[_,C]=(0,t.useState)(null),{toast:y}=(0,D.dj)(),w=(e,a,s)=>{j(i=>({...i,[e]:{...i[e],[a]:s}}))},E=async()=>{try{A(!0),C(null);let e="",i={};"POSA"===n?(e="dati-posa",i={dati_posa:v}):("COLLEGAMENTO_PARTENZA"===n||"COLLEGAMENTO_ARRIVO"===n)&&(e="dati-collegamento",i={dati_collegamento:v}),await u.CV.updateDatiComanda(s,e,i);let t="POSA"===n?"Metri posati inseriti con successo":"Metri collegati inseriti con successo";c?.(t),y({title:"Successo",description:t}),a()}catch(a){let e=a.response?.data?.detail||a.message||"Errore nel salvataggio";C(e),d?.(e),y({title:"Errore",description:e,variant:"destructive"})}finally{A(!1)}};return(0,i.jsx)(x.lG,{open:e,onOpenChange:a,children:(0,i.jsxs)(x.Cf,{className:"max-w-4xl max-h-[80vh] overflow-y-auto",children:[(0,i.jsxs)(x.c7,{children:[(0,i.jsxs)(x.L3,{className:"flex items-center gap-2",children:[(0,i.jsx)(G.A,{className:"h-5 w-5 text-blue-600"}),(()=>{switch(n){case"POSA":return"Inserisci Metri Posati";case"COLLEGAMENTO_PARTENZA":return"Inserisci Metri Collegati - Partenza";case"COLLEGAMENTO_ARRIVO":return"Inserisci Metri Collegati - Arrivo";default:return"Inserisci Metri"}})()]}),(0,i.jsxs)("p",{className:"text-sm text-gray-600",children:[(()=>{switch(n){case"POSA":return"Inserisci i metri realmente posati per ogni cavo";case"COLLEGAMENTO_PARTENZA":return"Inserisci i metri collegati lato partenza";case"COLLEGAMENTO_ARRIVO":return"Inserisci i metri collegati lato arrivo";default:return"Inserisci i metri"}})()," - Comanda: ",s]})]}),g?(0,i.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,i.jsx)(N.A,{className:"h-6 w-6 animate-spin mr-2"}),"Caricamento cavi..."]}):_?(0,i.jsxs)("div",{className:"flex items-center justify-center py-8 text-red-600",children:[(0,i.jsx)(B.A,{className:"h-5 w-5 mr-2"}),_]}):0===m.length?(0,i.jsx)("div",{className:"text-center py-8 text-gray-500",children:"Nessun cavo trovato per questa comanda"}):(0,i.jsx)("div",{className:"space-y-4",children:m.map(e=>(0,i.jsxs)("div",{className:"border rounded-lg p-4 bg-gray-50",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-semibold text-blue-600",children:e.id_cavo}),(0,i.jsxs)("p",{className:"text-sm text-gray-600",children:[e.tipologia," - ",e.formazione," - ",e.metratura_teorica,"m teorici"]})]}),(0,i.jsx)(l.E,{variant:"Installato"===e.stato_installazione?"default":"secondary",children:e.stato_installazione})]}),(0,i.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(h.J,{htmlFor:`metri-${e.id_cavo}`,children:"POSA"===n?"Metri Posati":"Metri Collegati"}),(0,i.jsx)(o.p,{id:`metri-${e.id_cavo}`,type:"number",min:"0",step:"0.1",value:v[e.id_cavo]?.metratura_reale||0,onChange:a=>w(e.id_cavo,"metratura_reale",parseFloat(a.target.value)||0),className:"mt-1"})]}),"POSA"===n&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(h.J,{htmlFor:`persone-${e.id_cavo}`,children:"Persone Impiegate"}),(0,i.jsx)(o.p,{id:`persone-${e.id_cavo}`,type:"number",min:"1",value:v[e.id_cavo]?.numero_persone_impiegate||1,onChange:a=>w(e.id_cavo,"numero_persone_impiegate",parseInt(a.target.value)||1),className:"mt-1"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(h.J,{htmlFor:`sistemazione-${e.id_cavo}`,children:"Sistemazione"}),(0,i.jsx)(o.p,{id:`sistemazione-${e.id_cavo}`,value:v[e.id_cavo]?.sistemazione||"",onChange:a=>w(e.id_cavo,"sistemazione",a.target.value),className:"mt-1",placeholder:"Es: Interrato, Aereo..."})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(h.J,{htmlFor:`fascettatura-${e.id_cavo}`,children:"Fascettatura"}),(0,i.jsx)(o.p,{id:`fascettatura-${e.id_cavo}`,value:v[e.id_cavo]?.fascettatura||"",onChange:a=>w(e.id_cavo,"fascettatura",a.target.value),className:"mt-1",placeholder:"Es: Standard, Rinforzata..."})]})]})]})]},e.id_cavo))}),(0,i.jsxs)("div",{className:"flex justify-end gap-2 pt-4 border-t",children:[(0,i.jsx)(r.$,{variant:"outline",onClick:a,disabled:b,children:"Annulla"}),(0,i.jsx)(r.$,{onClick:E,disabled:b||0===m.length,className:"bg-blue-600 hover:bg-blue-700",children:b?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(N.A,{className:"h-4 w-4 animate-spin mr-2"}),"Salvando..."]}):"Salva Metri"})]})]})})}var U=s(56896);let H=(0,R.A)("wrench",[["path",{d:"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z",key:"cbrjhi"}]]);function W({open:e,onClose:a,codiceComanda:s,onSuccess:n,onError:c}){let[d,u]=(0,t.useState)([]),[p,g]=(0,t.useState)([]),[b,A]=(0,t.useState)({}),[_,C]=(0,t.useState)({}),[y,w]=(0,t.useState)(!1),[E,O]=(0,t.useState)(!1),[z,S]=(0,t.useState)(null),[R,I]=(0,t.useState)(""),[T,k]=(0,t.useState)(0),[$,L]=(0,t.useState)([]),[P,M]=(0,t.useState)(null),{toast:V}=(0,D.dj)(),{cantiere:F}=(0,m.A)(),q=(e,a)=>{let s=parseFloat(a)||0;A(a=>{let i={...a,[e]:{...a[e],metratura_reale:s}};if(R&&"BOBINA_VUOTA"!==R){let a=p.find(e=>e.id_bobina===R);if(a){let s=0;Object.keys(i).forEach(e=>{let a=i[e]?.metratura_reale||0;a>0&&(s+=a)});let t=a.metri_residui-s;if(k(t),t<0&&!P){M(e);let a=[];Object.keys(i).forEach(s=>{0===(i[s]?.metratura_reale||0)&&s!==e&&a.push(s)}),L(a)}else t>=0&&P===e&&(M(null),L([]))}}return i}),Z(e,s)},Z=(e,a)=>{let s=d.find(a=>a.id_cavo===e);s&&C(i=>{let t={...i};return delete t[e],a>0&&s.metratura_teorica,t})},B=(e,a)=>{let s=parseInt(a)||1;A(a=>({...a,[e]:{...a[e],numero_persone_impiegate:s}}))},J=(e,a)=>{A(s=>({...s,[e]:{...s[e],sistemazione:a}}))},W=(e,a)=>{A(s=>({...s,[e]:{...s[e],fascettatura:a}}))},X=async()=>{try{if(O(!0),S(null),Object.keys(_).length>0)return void S("Correggere gli errori di validazione prima di salvare");let e={};if(Object.keys(b).forEach(a=>{let s=b[a];if((s?.metratura_reale||0)>0){let i=P===a||T<0;e[a]={...s,id_bobina:R||"BOBINA_VUOTA",force_over:i}}}),0===Object.keys(e).length)return void S("Inserire almeno un metro per almeno un cavo");console.log("\uD83D\uDCBE InserisciMetriPosatiDialog: Salvataggio dati posa:",{codiceComanda:s,caviDaSalvare:Object.keys(e).length,datiPosaFiltrati:e,selectedBobina:R,metriResiduiSimulati:T,cavoCheCausaOver:P});let i=await fetch(`/api/comande/${s}/dati-posa-bobine`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("token")}`},body:JSON.stringify(e)});if(!i.ok){let e=await i.json();throw Error(e.detail||"Errore durante il salvataggio")}let t=`Metri posati inseriti con successo per ${Object.keys(e).length} cavi`;n?.(t),V({title:"Successo",description:t}),a()}catch(a){let e=a?.response?.data?.detail||"Errore durante il salvataggio dei metri posati";S(e),c?.(e)}finally{O(!1)}};return(0,i.jsx)(x.lG,{open:e,onOpenChange:a,children:(0,i.jsxs)(x.Cf,{className:"max-w-6xl max-h-[90vh] overflow-y-auto",children:[(0,i.jsxs)(x.c7,{children:[(0,i.jsxs)(x.L3,{className:"flex items-center gap-2",children:[(0,i.jsx)(H,{className:"h-5 w-5 text-blue-600"}),"Inserisci Metri Posati - Comanda ",s]}),(0,i.jsx)("p",{className:"text-sm text-gray-600",children:"Inserisci i metri posati per ogni cavo della comanda POSA"})]}),z&&(0,i.jsxs)(j.Fc,{variant:"destructive",children:[(0,i.jsx)(f.A,{className:"h-4 w-4"}),(0,i.jsx)(j.TN,{children:z})]}),y?(0,i.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,i.jsx)(N.A,{className:"h-6 w-6 animate-spin mr-2"}),"Caricamento cavi..."]}):(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"border rounded-lg p-4 bg-blue-50",children:[(0,i.jsx)("h3",{className:"font-medium text-blue-900 mb-3",children:"Selezione Bobina Principale"}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(h.J,{htmlFor:"bobina-principale",children:"Bobina da Utilizzare"}),(0,i.jsxs)(v.l6,{value:R,onValueChange:e=>{if(I(e),L([]),M(null),A(a=>{let s={...a};return Object.keys(s).forEach(a=>{s[a]={...s[a],id_bobina:e}}),s}),e&&"BOBINA_VUOTA"!==e){let a=p.find(a=>a.id_bobina===e);a&&k(a.metri_residui)}else k(0)},children:[(0,i.jsx)(v.bq,{children:(0,i.jsx)(v.yv,{placeholder:"Seleziona bobina principale..."})}),(0,i.jsxs)(v.gC,{children:[(0,i.jsx)(v.eb,{value:"BOBINA_VUOTA",children:"\uD83D\uDD04 BOBINA_VUOTA (Assegna dopo)"}),p.map(e=>(0,i.jsxs)(v.eb,{value:e.id_bobina,children:["✅ ",e.id_bobina," - ",e.tipologia," ",e.sezione," (",e.metri_residui,"m)"]},e.id_bobina))]})]})]}),R&&"BOBINA_VUOTA"!==R&&(0,i.jsxs)("div",{className:"flex items-center gap-4",children:[(0,i.jsxs)("div",{className:"text-sm",children:[(0,i.jsx)("span",{className:"font-medium",children:"Metri Residui: "}),(0,i.jsxs)("span",{className:T<0?"text-red-600 font-bold":"text-green-600",children:[T.toFixed(1),"m"]})]}),T<0&&(0,i.jsx)(l.E,{variant:"destructive",className:"text-xs",children:"OVER"})]})]})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("h3",{className:"font-medium",children:["Cavi da Installare (",d.length,")"]}),d.map(e=>{let a=b[e.id_cavo],s=_[e.id_cavo],t=$.includes(e.id_cavo),n=P===e.id_cavo,r=T<0&&"BOBINA_VUOTA"!==R;return(0,i.jsxs)("div",{className:`border rounded-lg p-4 space-y-4 ${t?"bg-red-50 border-red-200":n?"bg-orange-50 border-orange-200":""}`,children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsxs)("h4",{className:"font-medium flex items-center gap-2",children:[e.id_cavo,t&&(0,i.jsx)(l.E,{variant:"destructive",className:"text-xs",children:"BLOCCATO"}),n&&(0,i.jsx)(l.E,{variant:"outline",className:"text-xs border-orange-500 text-orange-700",children:"CAUSA OVER"})]}),(0,i.jsxs)("p",{className:"text-sm text-gray-600",children:[e.tipologia," - ",e.formazione," - ",e.metratura_teorica,"m teorici"]})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(l.E,{variant:"Installato"===e.stato_installazione?"default":"secondary",children:e.stato_installazione}),r&&(0,i.jsx)(l.E,{variant:"destructive",className:"text-xs",children:"OVER"})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(h.J,{htmlFor:`metri-${e.id_cavo}`,children:"Metri Posati *"}),(0,i.jsx)(o.p,{id:`metri-${e.id_cavo}`,type:"number",min:"0",step:"0.1",value:a?.metratura_reale||"",onChange:a=>q(e.id_cavo,a.target.value),className:s?"border-red-500":t?"border-red-300 bg-red-50":"",placeholder:t?"Bloccato (OVER)":"0.0",disabled:t}),s&&(0,i.jsx)("p",{className:"text-xs text-red-500 mt-1",children:s}),t&&(0,i.jsx)("p",{className:"text-xs text-red-600 mt-1",children:"⚠️ Cavo bloccato: bobina in stato OVER"}),n&&!t&&(0,i.jsx)("p",{className:"text-xs text-orange-600 mt-1",children:"⚠️ Questo cavo causa lo stato OVER della bobina"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(h.J,{htmlFor:`persone-${e.id_cavo}`,children:"Persone Impiegate"}),(0,i.jsx)(o.p,{id:`persone-${e.id_cavo}`,type:"number",min:"1",value:a?.numero_persone_impiegate||1,onChange:a=>B(e.id_cavo,a.target.value)})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(U.S,{id:`sistemazione-${e.id_cavo}`,checked:a?.sistemazione||!1,onCheckedChange:a=>J(e.id_cavo,!!a)}),(0,i.jsx)(h.J,{htmlFor:`sistemazione-${e.id_cavo}`,children:"Sistemazione"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(U.S,{id:`fascettatura-${e.id_cavo}`,checked:a?.fascettatura||!1,onCheckedChange:a=>W(e.id_cavo,!!a)}),(0,i.jsx)(h.J,{htmlFor:`fascettatura-${e.id_cavo}`,children:"Fascettatura"})]})]}),(0,i.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,i.jsx)("span",{className:"font-medium",children:"Bobina assegnata: "}),(0,i.jsx)("span",{className:"BOBINA_VUOTA"===R?"text-orange-600":"text-blue-600",children:R||"Nessuna"})]})]},e.id_cavo)})]})]}),(0,i.jsxs)("div",{className:"flex justify-end gap-2 pt-4 border-t",children:[(0,i.jsx)(r.$,{variant:"outline",onClick:a,children:"Annulla"}),(0,i.jsx)(r.$,{onClick:X,disabled:E||y||0===d.length,children:E?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(N.A,{className:"h-4 w-4 animate-spin mr-2"}),"Salvando..."]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(G.A,{className:"h-4 w-4 mr-2"}),"Salva Metri Posati"]})})]})]})})}var X=s(99270),K=s(13861);function Q(){let[e,a]=(0,t.useState)("active"),[s,x]=(0,t.useState)([]),[h,p]=(0,t.useState)([]),[v,j]=(0,t.useState)(!0),[f,b]=(0,t.useState)(""),[A,_]=(0,t.useState)(""),[C,y]=(0,t.useState)("all"),[z,S]=(0,t.useState)("all"),[R,I]=(0,t.useState)(!1),[T,P]=(0,t.useState)(!1),[M,V]=(0,t.useState)(!1),[F,G]=(0,t.useState)(!1),[q,U]=(0,t.useState)(!1),[H,Q]=(0,t.useState)(null),[Y,ee]=(0,t.useState)(null),{user:ea,cantiere:es}=(0,m.A)(),{toast:ei}=(0,D.dj)(),[et,en]=(0,t.useState)(0),er=async()=>{try{if(j(!0),b(""),!et||et<=0)return void b("Cantiere non selezionato");let[e,a]=await Promise.all([u.CV.getComande(et),u.AR.getResponsabili(et)]),s=e?.data?.comande||e?.comande||e?.data||e||[],i=a?.data||a||[];x(Array.isArray(s)?s:[]),p(Array.isArray(i)?i:[])}catch(e){b(e.response?.data?.detail||"Errore durante il caricamento dei dati")}finally{j(!1)}},el=e=>{ei({title:"Successo",description:e}),er()},eo=e=>{ei({title:"Errore",description:e,variant:"destructive"})},ec=async e=>{if(confirm(`Sei sicuro di voler eliminare la comanda ${e}?`))try{j(!0),await u.CV.deleteComanda(et,e),el(`Comanda ${e} eliminata con successo`)}catch(e){eo("Errore durante l'eliminazione della comanda")}finally{j(!1)}},ed=e=>{switch(e){case"COMPLETATA":return(0,i.jsx)(l.E,{className:"bg-green-100 text-green-800",children:"Completata"});case"IN_CORSO":return(0,i.jsx)(l.E,{className:"bg-blue-100 text-blue-800",children:"In Corso"});case"ASSEGNATA":return(0,i.jsx)(l.E,{className:"bg-yellow-100 text-yellow-800",children:"Assegnata"});case"CREATA":return(0,i.jsx)(l.E,{className:"bg-gray-100 text-gray-800",children:"Creata"});case"ANNULLATA":return(0,i.jsx)(l.E,{className:"bg-red-100 text-red-800",children:"Annullata"});default:return(0,i.jsx)(l.E,{variant:"secondary",children:e})}},em=e=>{let a=(0,c.Fw)(e);return(0,i.jsx)(l.E,{className:a.badge,children:{POSA:"\uD83D\uDD27 Posa",COLLEGAMENTO_PARTENZA:"\uD83D\uDD0C Coll. Partenza",COLLEGAMENTO_ARRIVO:"⚡ Coll. Arrivo",CERTIFICAZIONE:"\uD83D\uDCCB Certificazione"}[e]||e.replace(/_/g," ")})},eu=Array.isArray(s)?s.filter(a=>{let s=!0;switch(e){case"active":s="IN_CORSO"===a.stato||"ASSEGNATA"===a.stato||"CREATA"===a.stato;break;case"completed":s="COMPLETATA"===a.stato;break;default:s=!0}let i=""===A||a.codice_comanda.toLowerCase().includes(A.toLowerCase())||a.descrizione&&a.descrizione.toLowerCase().includes(A.toLowerCase())||a.responsabile&&a.responsabile.toLowerCase().includes(A.toLowerCase()),t="all"===C||a.responsabile===C,n="all"===z||a.tipo_comanda===z;return s&&i&&t&&n}):[],ex={totali:Array.isArray(s)?s.length:0,in_corso:Array.isArray(s)?s.filter(e=>"IN_CORSO"===e.stato).length:0,completate:Array.isArray(s)?s.filter(e=>"COMPLETATA"===e.stato).length:0,pianificate:Array.isArray(s)?s.filter(e=>"CREATA"===e.stato||"ASSEGNATA"===e.stato).length:0,filtrate:eu.length};return(0,i.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:[(0,i.jsxs)("div",{className:"max-w-[90%] mx-auto space-y-6",children:[(0,i.jsxs)("div",{className:"mb-6",children:[(0,i.jsx)("h1",{className:"text-2xl font-bold text-slate-900 mb-2",children:"Gestione Comande"}),(0,i.jsx)("p",{className:"text-slate-600",children:et>0?`Cantiere ${et}`:"Nessun cantiere selezionato"})]}),(0,i.jsx)("div",{className:"mb-6",children:(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)(X.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,i.jsx)(o.p,{placeholder:"Cerca per codice, responsabile, tipo, stato o descrizione...",value:A,onChange:e=>_(e.target.value),className:"pl-10 bg-gray-50 hover:bg-blue-50 focus:bg-white transition-colors"})]})}),(0,i.jsxs)("div",{className:"flex flex-wrap gap-2 mb-6",children:[(0,i.jsxs)(r.$,{onClick:()=>I(!0),className:"bg-blue-600 hover:bg-blue-700 text-white",children:[(0,i.jsx)(O.A,{className:"h-4 w-4 mr-2"}),"Nuova Comanda"]}),(0,i.jsxs)(r.$,{variant:"outline",onClick:()=>{ei({title:"Funzione in sviluppo",description:"L'assegnazione cavi sar\xe0 disponibile presto"})},disabled:0===eu.length,children:[(0,i.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Assegna Cavi"]}),(0,i.jsxs)(r.$,{variant:"outline",onClick:()=>P(!0),children:[(0,i.jsx)(E.A,{className:"h-4 w-4 mr-2"}),"Gestisci Responsabili"]})]}),(0,i.jsx)("div",{className:"mb-4",children:(0,i.jsxs)("h3",{className:"text-lg font-semibold text-gray-900",children:["Elenco Comande (",eu.length," di ",ex.totali,")"]})}),(0,i.jsx)(n.Zp,{className:"border border-gray-200 rounded-lg",children:(0,i.jsx)(n.Wu,{className:"p-0",children:(0,i.jsxs)(d.XI,{children:[(0,i.jsx)(d.A0,{children:(0,i.jsxs)(d.Hj,{className:"bg-gray-50",children:[(0,i.jsx)(d.nd,{className:"font-semibold",children:"Codice"}),(0,i.jsx)(d.nd,{className:"font-semibold",children:"Tipo"}),(0,i.jsx)(d.nd,{className:"font-semibold",children:"Responsabile"}),(0,i.jsx)(d.nd,{className:"font-semibold",children:"Contatti"}),(0,i.jsx)(d.nd,{className:"font-semibold",children:"Stato"}),(0,i.jsx)(d.nd,{className:"font-semibold",children:"Data Creazione"}),(0,i.jsx)(d.nd,{className:"font-semibold text-center",children:"Cavi"}),(0,i.jsx)(d.nd,{className:"font-semibold text-center",children:"Completamento"}),(0,i.jsx)(d.nd,{className:"font-semibold text-center",children:"Azioni"})]})}),(0,i.jsx)(d.BF,{children:v?(0,i.jsx)(d.Hj,{children:(0,i.jsx)(d.nA,{colSpan:9,className:"text-center py-8",children:(0,i.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,i.jsx)(N.A,{className:"h-4 w-4 animate-spin"}),"Caricamento comande..."]})})}):f?(0,i.jsx)(d.Hj,{children:(0,i.jsx)(d.nA,{colSpan:9,className:"text-center py-8",children:(0,i.jsxs)("div",{className:"flex items-center justify-center gap-2 text-red-600",children:[(0,i.jsx)(B.A,{className:"h-4 w-4"}),f]})})}):0===eu.length?(0,i.jsx)(d.Hj,{children:(0,i.jsx)(d.nA,{colSpan:9,className:"text-center py-8 text-slate-500",children:"Nessuna comanda trovata"})}):eu.map(e=>(0,i.jsxs)(d.Hj,{className:"hover:bg-gray-50",children:[(0,i.jsx)(d.nA,{children:(0,i.jsx)("div",{className:"font-semibold text-blue-600",children:e.codice_comanda})}),(0,i.jsx)(d.nA,{children:em(e.tipo_comanda)}),(0,i.jsx)(d.nA,{children:(0,i.jsx)("div",{className:"font-medium",children:e.responsabile||"Non assegnato"})}),(0,i.jsx)(d.nA,{children:(0,i.jsxs)("div",{className:"text-sm text-gray-600",children:[e.responsabile_telefono&&(0,i.jsxs)("div",{children:["\uD83D\uDCDE ",e.responsabile_telefono]}),e.responsabile_email&&(0,i.jsxs)("div",{children:["✉️ ",e.responsabile_email]})]})}),(0,i.jsx)(d.nA,{children:ed(e.stato)}),(0,i.jsx)(d.nA,{children:(0,i.jsx)("div",{className:"text-sm",children:new Date(e.data_creazione).toLocaleDateString("it-IT")})}),(0,i.jsx)(d.nA,{className:"text-center",children:(0,i.jsx)("div",{className:"font-semibold text-blue-600",children:e.numero_cavi_assegnati||0})}),(0,i.jsx)(d.nA,{className:"text-center",children:(0,i.jsxs)("div",{className:"font-semibold",children:[(e.percentuale_completamento||0).toFixed(1),"%"]})}),(0,i.jsx)(d.nA,{className:"text-center",children:(0,i.jsxs)("div",{className:"flex gap-1 justify-center",children:[(0,i.jsx)(r.$,{variant:"ghost",size:"sm",onClick:()=>{Q(e.codice_comanda),V(!0)},title:"Visualizza",children:(0,i.jsx)(K.A,{className:"h-4 w-4"})}),(0,i.jsx)(r.$,{variant:"ghost",size:"sm",onClick:()=>{ei({title:"Funzione in sviluppo",description:"La modifica comande sar\xe0 disponibile presto"})},title:"Modifica",children:(0,i.jsx)(k.A,{className:"h-4 w-4"})}),["POSA","COLLEGAMENTO_PARTENZA","COLLEGAMENTO_ARRIVO"].includes(e.tipo_comanda)&&(0,i.jsx)(r.$,{variant:"ghost",size:"sm",onClick:()=>{Q(e.codice_comanda),ee(e.tipo_comanda),"POSA"===e.tipo_comanda?U(!0):G(!0)},title:"POSA"===e.tipo_comanda?"Inserisci Metri Posati":"Inserisci Metri Collegati",children:(0,i.jsx)(g.A,{className:"h-4 w-4"})}),(0,i.jsx)(r.$,{variant:"ghost",size:"sm",onClick:()=>ec(e.codice_comanda),disabled:v,className:"text-red-600 hover:text-red-700",title:"Elimina",children:(0,i.jsx)($.A,{className:"h-4 w-4"})})]})})]},e.codice_comanda))})]})})})]}),(0,i.jsx)(w,{open:R,onClose:()=>I(!1),onSuccess:el,onError:eo,onComandaCreated:()=>er()}),(0,i.jsx)(L,{open:T,onClose:()=>P(!1),onSuccess:el,onError:eo}),(0,i.jsx)(Z,{open:M,onClose:()=>{V(!1),Q(null)},codiceComanda:H,onSuccess:el,onError:eo}),(0,i.jsx)(J,{open:F,onClose:()=>{G(!1),Q(null),ee(null)},codiceComanda:H||"",tipoComanda:Y||"POSA",onSuccess:e=>{el(e),loadComande()},onError:eo}),(0,i.jsx)(W,{open:q,onClose:()=>{U(!1),Q(null),ee(null)},codiceComanda:H||"",onSuccess:e=>{el(e),loadComande()},onError:eo})]})}},83997:e=>{"use strict";e.exports=require("tty")},88233:(e,a,s)=>{"use strict";s.d(a,{A:()=>i});let i=(0,s(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},94735:e=>{"use strict";e.exports=require("events")},96474:(e,a,s)=>{"use strict";s.d(a,{A:()=>i});let i=(0,s(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};var a=require("../../webpack-runtime.js");a.C(e);var s=e=>a(a.s=e),i=a.X(0,[447,757,658,841,400,995,891,223,109],()=>s(59316));module.exports=i})();