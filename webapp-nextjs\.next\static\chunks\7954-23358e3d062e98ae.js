"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7954],{24944:(e,t,r)=>{r.d(t,{k:()=>o});var a=r(95155);r(12115);var n=r(55863),i=r(59434);function o(e){let{className:t,value:r,...o}=e;return(0,a.jsx)(n.bL,{"data-slot":"progress",className:(0,i.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",t),...o,children:(0,a.jsx)(n.C1,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:"translateX(-".concat(100-(r||0),"%)")}})})}},30285:(e,t,r)=>{r.d(t,{$:()=>l});var a=r(95155);r(12115);var n=r(99708),i=r(74466),o=r(59434);let s=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:r,size:i,asChild:l=!1,...d}=e,u=l?n.DX:"button";return(0,a.jsx)(u,{"data-slot":"button",className:(0,o.cn)(s({variant:r,size:i,className:t})),...d})}},32919:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},35169:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},40968:(e,t,r)=>{r.d(t,{b:()=>s});var a=r(12115),n=r(63655),i=r(95155),o=a.forwardRef((e,t)=>(0,i.jsx)(n.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));o.displayName="Label";var s=o},46081:(e,t,r)=>{r.d(t,{A:()=>o,q:()=>i});var a=r(12115),n=r(95155);function i(e,t){let r=a.createContext(t),i=e=>{let{children:t,...i}=e,o=a.useMemo(()=>i,Object.values(i));return(0,n.jsx)(r.Provider,{value:o,children:t})};return i.displayName=e+"Provider",[i,function(n){let i=a.useContext(r);if(i)return i;if(void 0!==t)return t;throw Error(`\`${n}\` must be used within \`${e}\``)}]}function o(e,t=[]){let r=[],i=()=>{let t=r.map(e=>a.createContext(e));return function(r){let n=r?.[e]||t;return a.useMemo(()=>({[`__scope${e}`]:{...r,[e]:n}}),[r,n])}};return i.scopeName=e,[function(t,i){let o=a.createContext(i),s=r.length;r=[...r,i];let l=t=>{let{scope:r,children:i,...l}=t,d=r?.[e]?.[s]||o,u=a.useMemo(()=>l,Object.values(l));return(0,n.jsx)(d.Provider,{value:u,children:i})};return l.displayName=t+"Provider",[l,function(r,n){let l=n?.[e]?.[s]||o,d=a.useContext(l);if(d)return d;if(void 0!==i)return i;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let n=r.reduce((t,{useScope:r,scopeName:a})=>{let n=r(e)[`__scope${a}`];return{...t,...n}},{});return a.useMemo(()=>({[`__scope${t.scopeName}`]:n}),[n])}};return r.scopeName=t.scopeName,r}(i,...t)]}},54861:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},55365:(e,t,r)=>{r.d(t,{Fc:()=>l,TN:()=>d});var a=r(95155),n=r(12115),i=r(74466),o=r(59434);let s=(0,i.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),l=n.forwardRef((e,t)=>{let{className:r,variant:n,...i}=e;return(0,a.jsx)("div",{ref:t,role:"alert",className:(0,o.cn)(s({variant:n}),r),...i})});l.displayName="Alert",n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("h5",{ref:t,className:(0,o.cn)("mb-1 font-medium leading-none tracking-tight",r),...n})}).displayName="AlertTitle";let d=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,o.cn)("text-sm [&_p]:leading-relaxed",r),...n})});d.displayName="AlertDescription"},55863:(e,t,r)=>{r.d(t,{C1:()=>w,bL:()=>y});var a=r(12115),n=r(46081),i=r(63655),o=r(95155),s="Progress",[l,d]=(0,n.A)(s),[u,c]=l(s),v=a.forwardRef((e,t)=>{var r,a,n,s;let{__scopeProgress:l,value:d=null,max:c,getValueLabel:v=m,...f}=e;(c||0===c)&&!b(c)&&console.error((r="".concat(c),a="Progress","Invalid prop `max` of value `".concat(r,"` supplied to `").concat(a,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let p=b(c)?c:100;null===d||h(d,p)||console.error((n="".concat(d),s="Progress","Invalid prop `value` of value `".concat(n,"` supplied to `").concat(s,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let y=h(d,p)?d:null,w=x(y)?v(y,p):void 0;return(0,o.jsx)(u,{scope:l,value:y,max:p,children:(0,o.jsx)(i.sG.div,{"aria-valuemax":p,"aria-valuemin":0,"aria-valuenow":x(y)?y:void 0,"aria-valuetext":w,role:"progressbar","data-state":g(y,p),"data-value":null!=y?y:void 0,"data-max":p,...f,ref:t})})});v.displayName=s;var f="ProgressIndicator",p=a.forwardRef((e,t)=>{var r;let{__scopeProgress:a,...n}=e,s=c(f,a);return(0,o.jsx)(i.sG.div,{"data-state":g(s.value,s.max),"data-value":null!=(r=s.value)?r:void 0,"data-max":s.max,...n,ref:t})});function m(e,t){return"".concat(Math.round(e/t*100),"%")}function g(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function x(e){return"number"==typeof e}function b(e){return x(e)&&!isNaN(e)&&e>0}function h(e,t){return x(e)&&!isNaN(e)&&e<=t&&e>=0}p.displayName=f;var y=v,w=p},59434:(e,t,r)=>{r.d(t,{cn:()=>i});var a=r(52596),n=r(39688);function i(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n.QP)((0,a.$)(t))}},62523:(e,t,r)=>{r.d(t,{p:()=>i});var a=r(95155);r(12115);var n=r(59434);function i(e){let{className:t,type:r,...i}=e;return(0,a.jsx)("input",{type:r,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...i})}},63655:(e,t,r)=>{r.d(t,{hO:()=>l,sG:()=>s});var a=r(12115),n=r(47650),i=r(99708),o=r(95155),s=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,i.TL)(`Primitive.${t}`),n=a.forwardRef((e,a)=>{let{asChild:n,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(n?r:t,{...i,ref:a})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function l(e,t){e&&n.flushSync(()=>e.dispatchEvent(t))}},66695:(e,t,r)=>{r.d(t,{BT:()=>l,Wu:()=>d,ZB:()=>s,Zp:()=>i,aR:()=>o});var a=r(95155);r(12115);var n=r(59434);function i(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function o(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function s(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",t),...r})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",t),...r})}function d(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",t),...r})}},75525:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},78749:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},85057:(e,t,r)=>{r.d(t,{J:()=>o});var a=r(95155);r(12115);var n=r(40968),i=r(59434);function o(e){let{className:t,...r}=e;return(0,a.jsx)(n.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...r})}},92657:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}}]);