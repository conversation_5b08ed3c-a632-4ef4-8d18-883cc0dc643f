'use client'

import { useMemo, useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Cable,
  CheckCircle,
  Clock,
  AlertTriangle,
  Zap,
  Package,
  BarChart3,
  Filter,
  X
} from 'lucide-react'
import { Cavo } from '@/types'
import { KpiTooltip } from './tooltips/CableTooltips'

interface CaviStatisticsProps {
  cavi: Cavo[]
  filteredCavi: Cavo[]
  className?: string
  revisioneCorrente?: string
}

export default function CaviStatistics({
  cavi,
  filteredCavi,
  className,
  revisioneCorrente
}: CaviStatisticsProps) {
  const stats = useMemo(() => {
    const totalCavi = cavi.length
    const filteredCount = filteredCavi.length
    
    // Installation status
    const installati = filteredCavi.filter(c => 
      c.stato_installazione === 'Installato' || 
      (c.metri_posati && c.metri_posati > 0) ||
      (c.metratura_reale && c.metratura_reale > 0)
    ).length
    
    const inCorso = filteredCavi.filter(c => 
      c.stato_installazione === 'In corso'
    ).length
    
    const daInstallare = filteredCount - installati - inCorso
    
    // Connection status
    const collegati = filteredCavi.filter(c => {
      const collegamento = c.collegamento || c.collegamenti || 0
      return collegamento === 3 // Both sides connected
    }).length
    
    const parzialmenteCollegati = filteredCavi.filter(c => {
      const collegamento = c.collegamento || c.collegamenti || 0
      return collegamento === 1 || collegamento === 2 // One side connected
    }).length
    
    const nonCollegati = filteredCavi.filter(c => {
      const collegamento = c.collegamento || c.collegamenti || 0
      return collegamento === 0 && (c.metri_posati > 0 || c.metratura_reale > 0)
    }).length
    
    // Certification status
    const certificati = filteredCavi.filter(c => 
      c.certificato === true || 
      c.certificato === 'SI' || 
      c.certificato === 'CERTIFICATO'
    ).length
    
    // Meters calculation
    const metriTotali = filteredCavi.reduce((sum, c) => sum + (c.metri_teorici || 0), 0)
    const metriInstallati = filteredCavi.reduce((sum, c) => {
      const metri = c.metri_posati || c.metratura_reale || 0
      return sum + metri
    }, 0)

    // Calcolo IAP (Indice di Avanzamento Ponderato) come nella webapp originale
    const calculateIAP = (nTot: number, nInst: number, nColl: number, nCert: number): number => {
      // Pesi per le fasi del progetto
      const Wp = 2.0  // Peso fase Posa
      const Wc = 1.5  // Peso fase Collegamento
      const Wz = 0.5  // Peso fase Certificazione

      // Se non ci sono cavi, ritorna 0
      if (nTot === 0) return 0

      // Calcolo del numeratore (Sforzo Completato)
      const sforzoSoloInstallati = (nInst - nColl) * Wp
      const sforzoSoloCollegati = (nColl - nCert) * (Wp + Wc)
      const sforzoCertificati = nCert * (Wp + Wc + Wz)
      const numeratore = sforzoSoloInstallati + sforzoSoloCollegati + sforzoCertificati

      // Calcolo del denominatore (Sforzo Massimo Previsto)
      const denominatore = nTot * (Wp + Wc + Wz)

      // Calcolo finale dell'IAP in percentuale
      const iap = (numeratore / denominatore) * 100

      return Math.round(iap * 100) / 100 // Arrotonda a 2 decimali
    }

    const percentualeInstallazione = calculateIAP(filteredCount, installati, collegati, certificati)
    
    return {
      totalCavi,
      filteredCount,
      installati,
      inCorso,
      daInstallare,
      collegati,
      parzialmenteCollegati,
      nonCollegati,
      certificati,
      metriTotali,
      metriInstallati,
      percentualeInstallazione
    }
  }, [cavi, filteredCavi])

  // Removed click-to-filter functionality as per user request

  const clearFilter = () => {
    if (onFilterChange) {
      onFilterChange(null)
    }
  }

  return (
    <Card className={className}>
      <CardContent className="p-1.5">
        {/* Header with revision and filter indicator */}
        <div className="flex items-center justify-between mb-1">
          <div className="flex items-center space-x-1.5">
            <BarChart3 className="h-3.5 w-3.5 text-mariner-600" />
            <span className="text-xs font-semibold text-mariner-900">Statistiche Cavi</span>
            {activeFilter && (
              <div className="flex items-center space-x-1">
                <Filter className="h-3 w-3 text-blue-600" />
                <span className="text-xs text-blue-600 font-medium">Filtro attivo</span>
              </div>
            )}
          </div>
          <div className="flex items-center space-x-1">
            {activeFilter && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearFilter}
                className="h-5 px-1.5 text-xs text-blue-600 hover:text-blue-800"
              >
                <X className="h-3 w-3 mr-1" />
                Rimuovi filtro
              </Button>
            )}
            {revisioneCorrente && (
              <Badge variant="outline" className="text-xs font-medium py-0 px-1.5 h-5">
                Rev. {revisioneCorrente}
              </Badge>
            )}
          </div>
        </div>

        {/* Statistics distributed across full width */}
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-2">

          {/* Total cables */}
          <KpiTooltip type="total" count={stats.totalCavi}>
            <div className="flex items-center space-x-1.5 bg-mariner-50 px-1.5 py-1 rounded-lg">
              <Cable className="h-3.5 w-3.5 text-mariner-600" />
              <div>
                <div className="font-bold text-mariner-900 text-sm">{stats.filteredCount}</div>
                <div className="text-xs text-mariner-600">di {stats.totalCavi} cavi</div>
              </div>
            </div>
          </KpiTooltip>

          {/* Installation status */}
          <KpiTooltip
            type="installed"
            count={stats.installati}
            percentage={(stats.installati / stats.filteredCount) * 100}
          >
            <div
              className="flex items-center space-x-1.5 bg-green-50 px-1.5 py-1 rounded-lg"
              aria-label={`Cavi installati: ${stats.installati} cavi`}
            >
              <CheckCircle className="h-3.5 w-3.5 text-green-600" />
              <div>
                <div className="font-bold text-green-700 text-sm">{stats.installati}</div>
                <div className="text-xs text-green-600">installati</div>
              </div>
            </div>
          </KpiTooltip>

          <KpiTooltip
            type="in_progress"
            count={stats.inCorso}
            percentage={(stats.inCorso / stats.filteredCount) * 100}
          >
            <div
              className="flex items-center space-x-1.5 bg-yellow-50 px-1.5 py-1 rounded-lg"
              aria-label={`Cavi in corso: ${stats.inCorso} cavi`}
            >
              <Clock className="h-3.5 w-3.5 text-yellow-600" />
              <div>
                <div className="font-bold text-yellow-700 text-sm">{stats.inCorso}</div>
                <div className="text-xs text-yellow-600">in corso</div>
              </div>
            </div>
          </KpiTooltip>

          <KpiTooltip
            type="to_install"
            count={stats.daInstallare}
            percentage={(stats.daInstallare / stats.filteredCount) * 100}
          >
            <div
              className="flex items-center space-x-1.5 bg-gray-50 px-1.5 py-1 rounded-lg"
              aria-label={`Cavi da installare: ${stats.daInstallare} cavi`}
            >
              <AlertTriangle className="h-3.5 w-3.5 text-gray-600" />
              <div>
                <div className="font-bold text-gray-700 text-sm">{stats.daInstallare}</div>
                <div className="text-xs text-gray-600">da installare</div>
              </div>
            </div>
          </KpiTooltip>

          {/* Connection status */}
          <KpiTooltip
            type="connected"
            count={stats.collegati}
            percentage={(stats.collegati / stats.filteredCount) * 100}
          >
            <div
              className="flex items-center space-x-1.5 bg-blue-50 px-1.5 py-1 rounded-lg"
              aria-label={`Cavi collegati: ${stats.collegati} cavi`}
            >
              <Zap className="h-3.5 w-3.5 text-blue-600" />
              <div>
                <div className="font-bold text-blue-700 text-sm">{stats.collegati}</div>
                <div className="text-xs text-blue-600">collegati</div>
              </div>
            </div>
          </KpiTooltip>

          {/* Certification status */}
          <KpiTooltip
            type="certified"
            count={stats.certificati}
            percentage={(stats.certificati / stats.filteredCount) * 100}
          >
            <div
              className="flex items-center space-x-1.5 bg-purple-50 px-1.5 py-1 rounded-lg"
              aria-label={`Cavi certificati: ${stats.certificati} cavi`}
            >
              <Package className="h-3.5 w-3.5 text-purple-600" />
              <div>
                <div className="font-bold text-purple-700 text-sm">{stats.certificati}</div>
                <div className="text-xs text-purple-600">certificati</div>
              </div>
            </div>
          </KpiTooltip>

          {/* Meters progress */}
          <KpiTooltip
            type="total"
            count={stats.metriInstallati}
          >
            <div className="flex items-center space-x-1.5 bg-indigo-50 px-1.5 py-1 rounded-lg">
              <div className="h-3.5 w-3.5 flex items-center justify-center">
                <div className="h-2 w-2 bg-indigo-600 rounded-full"></div>
              </div>
              <div>
                <div className="font-bold text-indigo-700 text-sm">{stats.metriInstallati.toLocaleString()}m</div>
                <div className="text-xs text-indigo-600">di {stats.metriTotali.toLocaleString()}m</div>
              </div>
            </div>
          </KpiTooltip>

        </div>

        {/* IAP Progress bar - Colori morbidi */}
        {stats.filteredCount > 0 && (
          <div className="mt-2 bg-gray-50 p-2 rounded-lg">
            <div className="flex justify-between text-xs font-medium text-gray-700 mb-1">
              <span>IAP - Indice Avanzamento Ponderato</span>
              <span className={`font-bold ${
                stats.percentualeInstallazione >= 80 ? 'text-emerald-700' :
                stats.percentualeInstallazione >= 50 ? 'text-yellow-700' :
                stats.percentualeInstallazione >= 25 ? 'text-orange-700' : 'text-amber-700'
              }`}>
                {stats.percentualeInstallazione.toFixed(1)}%
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all duration-500 ease-in-out ${
                  stats.percentualeInstallazione >= 80 ? 'bg-gradient-to-r from-emerald-500 to-emerald-600' :
                  stats.percentualeInstallazione >= 50 ? 'bg-gradient-to-r from-yellow-500 to-yellow-600' :
                  stats.percentualeInstallazione >= 25 ? 'bg-gradient-to-r from-orange-500 to-orange-600' :
                  'bg-gradient-to-r from-amber-500 to-amber-600'
                }`}
                style={{ width: `${Math.min(stats.percentualeInstallazione, 100)}%` }}
              />
            </div>
            <div className="flex justify-between text-xs text-gray-500 mt-0.5">
              <span>Pesi: Posa(2.0) + Collegamento(1.5) + Certificazione(0.5)</span>
              <span>{stats.installati}I + {stats.collegati}C + {stats.certificati}Cert</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
