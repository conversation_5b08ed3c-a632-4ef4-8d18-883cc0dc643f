(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6222],{30285:(e,a,s)=>{"use strict";s.d(a,{$:()=>l});var t=s(95155);s(12115);var r=s(99708),n=s(74466),i=s(59434);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:a,variant:s,size:n,asChild:l=!1,...c}=e,d=l?r.DX:"button";return(0,t.jsx)(d,{"data-slot":"button",className:(0,i.cn)(o({variant:s,size:n,className:a})),...c})}},40321:(e,a,s)=>{Promise.resolve().then(s.bind(s,42712))},42712:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>P});var t=s(95155),r=s(12115),n=s(35695),i=s(66695),o=s(30285),l=s(62523),c=s(85057),d=s(85127),m=s(54165),x=s(40283),u=s(25731),h=s(51154),g=s(47924),p=s(84616),f=s(85339),b=s(23227),v=s(24357),j=s(78749),w=s(92657),N=s(72713),y=s(13717),_=s(92138),C=s(32919),z=s(381),A=s(40646);function P(){let{user:e,isAuthenticated:a,isLoading:s}=(0,x.A)(),P=(0,n.useRouter)(),[k,S]=(0,r.useState)([]),[E,F]=(0,r.useState)(!0),[I,J]=(0,r.useState)(""),[$,L]=(0,r.useState)({}),[M,O]=(0,r.useState)(!1),[D,G]=(0,r.useState)(""),[B,T]=(0,r.useState)(!1),[Z,H]=(0,r.useState)(!1),[X,R]=(0,r.useState)(!1),[U,V]=(0,r.useState)(!1),[W,q]=(0,r.useState)(null),[Q,Y]=(0,r.useState)({commessa:"",descrizione:"",nome_cliente:"",indirizzo_cantiere:"",citta_cantiere:"",nazione_cantiere:"",password_cantiere:"",codice_univoco:""}),[K,ee]=(0,r.useState)({currentPassword:"",newPassword:"",confirmPassword:""}),[ea,es]=(0,r.useState)("change"),[et,er]=(0,r.useState)(""),[en,ei]=(0,r.useState)(!1),[eo,el]=(0,r.useState)(!1),[ec,ed]=(0,r.useState)({}),[em,ex]=(0,r.useState)({});(0,r.useEffect)(()=>{s||a||P.push("/login")},[a,s,P]),(0,r.useEffect)(()=>{a&&eu()},[a]);let eu=async()=>{try{F(!0);let e=await u._I.getCantieri();S(e),await eh(e)}catch(e){J("Errore nel caricamento dei cantieri")}finally{F(!1)}},eh=async e=>{try{O(!0);let a=e.map(async e=>{try{let a=await u._I.getCantiereStatistics(e.id_cantiere);return{id:e.id_cantiere,stats:a}}catch(a){return console.error("Errore nel caricamento statistiche cantiere ".concat(e.id_cantiere,":"),a),{id:e.id_cantiere,stats:{percentuale_avanzamento:0}}}}),s=(await Promise.all(a)).reduce((e,a)=>{let{id:s,stats:t}=a;return e[s]=t,e},{});L(s)}catch(e){console.error("Errore nel caricamento delle statistiche:",e)}finally{O(!1)}},eg=async()=>{try{await u._I.createCantiere(Q),T(!1),Y({commessa:"",descrizione:"",nome_cliente:"",indirizzo_cantiere:"",citta_cantiere:"",nazione_cantiere:"",password_cantiere:"",codice_univoco:""}),eu()}catch(e){J("Errore nella creazione del cantiere")}},ep=async()=>{if(W)try{await u._I.updateCantiere(W.id_cantiere,Q),H(!1),q(null),eu()}catch(e){J("Errore nella modifica del cantiere")}},ef=e=>{localStorage.setItem("selectedCantiereId",e.id_cantiere.toString()),localStorage.setItem("selectedCantiereName",e.commessa),P.push("/cantieri/".concat(e.id_cantiere))},eb=e=>{q(e),Y({commessa:e.commessa||"",descrizione:e.descrizione||"",nome_cliente:e.nome_cliente||"",indirizzo_cantiere:e.indirizzo_cantiere||"",citta_cantiere:e.citta_cantiere||"",nazione_cantiere:e.nazione_cantiere||"",password_cantiere:e.password_cantiere||"",codice_univoco:e.codice_univoco||""}),H(!0)},ev=async()=>{if(W){if(K.newPassword!==K.confirmPassword)return void J("Le password non coincidono");if(!K.currentPassword)return void J("Inserisci la password attuale per confermare il cambio");if(!K.newPassword||K.newPassword.length<6)return void J("La nuova password deve essere di almeno 6 caratteri");try{F(!0),J("");let e=await fetch("".concat("http://localhost:8001","/api/cantieri/").concat(W.id_cantiere,"/change-password"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("access_token"))},body:JSON.stringify({password_attuale:K.currentPassword,password_nuova:K.newPassword,conferma_password:K.confirmPassword})});if(!e.ok){let a=await e.json();throw Error(a.detail||"Errore nel cambio password")}let a=await e.json();if(a.success)ee({currentPassword:"",newPassword:"",confirmPassword:""}),R(!1),J(""),alert(a.message||"Password cambiata con successo");else throw Error(a.message||"Errore nel cambio password")}catch(e){J(e instanceof Error?e.message:"Errore nel cambio password")}finally{F(!1)}}},ej=async e=>{try{await navigator.clipboard.writeText(e)}catch(e){}},ew=async e=>{let a=e.id_cantiere;if(ec[a])ed(e=>({...e,[a]:!1})),ex(e=>({...e,[a]:""}));else if(em[a])ed(e=>({...e,[a]:!0}));else try{el(!0);let e=localStorage.getItem("token")||localStorage.getItem("access_token"),s=await fetch("".concat("http://localhost:8001","/api/cantieri/").concat(a,"/view-password"),{method:"GET",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)}});if(!s.ok){let e=await s.json();throw Error(e.detail||"Errore nel recupero password")}let t=await s.json();ex(e=>({...e,[a]:t.password_cantiere})),ed(e=>({...e,[a]:!0}))}catch(e){J(e instanceof Error?e.message:"Errore nel recupero password")}finally{el(!1)}},eN=k.filter(e=>{var a,s;return e.commessa.toLowerCase().includes(D.toLowerCase())||(null==(a=e.descrizione)?void 0:a.toLowerCase().includes(D.toLowerCase()))||(null==(s=e.nome_cliente)?void 0:s.toLowerCase().includes(D.toLowerCase()))});return s?(0,t.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,t.jsx)(h.A,{className:"h-8 w-8 animate-spin"})}):(0,t.jsxs)("div",{className:"max-w-[90%] mx-auto p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsx)("div",{className:"flex items-center gap-4",children:(0,t.jsxs)("div",{className:"relative w-80",children:[(0,t.jsx)(g.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,t.jsx)(l.p,{placeholder:"Cerca per commessa, descrizione o cliente...",value:D,onChange:e=>G(e.target.value),className:"pl-8 w-full"})]})}),(0,t.jsxs)(m.lG,{open:B,onOpenChange:T,children:[(0,t.jsx)(m.zM,{asChild:!0,children:(0,t.jsxs)(o.$,{className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:[(0,t.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"Nuovo Cantiere"]})}),(0,t.jsxs)(m.Cf,{className:"sm:max-w-[425px]",children:[(0,t.jsxs)(m.c7,{children:[(0,t.jsx)(m.L3,{children:"Crea Nuovo Cantiere"}),(0,t.jsx)(m.rr,{children:"Inserisci i dettagli del nuovo cantiere"})]}),(0,t.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(c.J,{htmlFor:"commessa",className:"text-right",children:"Commessa"}),(0,t.jsx)(l.p,{id:"commessa",value:Q.commessa,onChange:e=>Y({...Q,commessa:e.target.value}),className:"col-span-3"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(c.J,{htmlFor:"descrizione",className:"text-right",children:"Descrizione"}),(0,t.jsx)(l.p,{id:"descrizione",value:Q.descrizione,onChange:e=>Y({...Q,descrizione:e.target.value}),className:"col-span-3"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(c.J,{htmlFor:"nome_cliente",className:"text-right",children:"Cliente"}),(0,t.jsx)(l.p,{id:"nome_cliente",value:Q.nome_cliente,onChange:e=>Y({...Q,nome_cliente:e.target.value}),className:"col-span-3"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(c.J,{htmlFor:"password_cantiere",className:"text-right",children:"Password"}),(0,t.jsx)(l.p,{id:"password_cantiere",type:"password",value:Q.password_cantiere,onChange:e=>Y({...Q,password_cantiere:e.target.value}),className:"col-span-3"})]})]}),(0,t.jsx)(m.Es,{children:(0,t.jsx)(o.$,{onClick:eg,className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:"Crea Cantiere"})})]})]})]}),I&&(0,t.jsx)("div",{className:"mb-4 p-4 border border-red-200 rounded-lg bg-red-50",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 text-red-600 mr-2"}),(0,t.jsx)("span",{className:"text-red-800",children:I})]})}),E?(0,t.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,t.jsx)(h.A,{className:"h-8 w-8 animate-spin"})}):0===eN.length?(0,t.jsx)(i.Zp,{children:(0,t.jsxs)(i.Wu,{className:"flex flex-col items-center justify-center py-8",children:[(0,t.jsx)(b.A,{className:"h-12 w-12 text-muted-foreground mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Nessun cantiere trovato"}),(0,t.jsx)("p",{className:"text-muted-foreground text-center mb-4",children:D?"Nessun cantiere corrisponde ai criteri di ricerca":"Crea il tuo primo cantiere per iniziare"}),!D&&(0,t.jsxs)(o.$,{onClick:()=>T(!0),className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:[(0,t.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"Crea Primo Cantiere"]})]})}):(0,t.jsx)(i.Zp,{children:(0,t.jsxs)(d.XI,{children:[(0,t.jsx)(d.A0,{children:(0,t.jsxs)(d.Hj,{className:"border-b border-gray-200",children:[(0,t.jsx)(d.nd,{className:"font-semibold text-gray-700",children:"Commessa"}),(0,t.jsx)(d.nd,{className:"font-semibold text-gray-700",children:"Descrizione"}),(0,t.jsx)(d.nd,{className:"font-semibold text-gray-700",children:"Cliente"}),(0,t.jsx)(d.nd,{className:"font-semibold text-gray-700",children:"Data Creazione"}),(0,t.jsx)(d.nd,{className:"font-semibold text-gray-700",children:"Codice Accesso"}),(0,t.jsx)(d.nd,{className:"font-semibold text-gray-700",children:"Password Cantiere"}),(0,t.jsx)(d.nd,{className:"font-semibold text-gray-700",children:"Avanzamento"}),(0,t.jsx)(d.nd,{className:"font-semibold text-gray-700 text-center",children:"Progresso %"}),(0,t.jsx)(d.nd,{className:"text-center font-semibold text-gray-700",children:"Azioni"})]})}),(0,t.jsx)(d.BF,{children:eN.map(e=>{var a,s,r,n,i,l,c,m,x,u;return(0,t.jsxs)(d.Hj,{className:"hover:bg-gray-50/50 transition-colors",children:[(0,t.jsx)(d.nA,{className:"font-semibold text-gray-900 py-4",children:e.commessa}),(0,t.jsx)(d.nA,{className:"text-gray-700 py-4",children:e.descrizione}),(0,t.jsx)(d.nA,{className:"text-gray-700 py-4",children:e.nome_cliente}),(0,t.jsx)(d.nA,{className:"text-gray-600 py-4",children:new Date(e.data_creazione).toLocaleDateString()}),(0,t.jsx)(d.nA,{className:"py-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("code",{className:"text-sm bg-blue-50 text-blue-700 px-3 py-1.5 rounded-md font-mono border border-blue-200",children:e.codice_univoco}),(0,t.jsx)(o.$,{size:"sm",variant:"ghost",className:"h-7 w-7 p-0 text-gray-400 hover:bg-gray-50 hover:text-gray-600 transition-colors",title:"Copia codice",onClick:()=>ej(e.codice_univoco),children:(0,t.jsx)(v.A,{className:"h-3 w-3"})})]})}),(0,t.jsx)(d.nA,{className:"py-4",children:(0,t.jsx)("div",{className:"flex items-center gap-3",children:(0,t.jsx)("div",{className:"flex items-center gap-2",children:ec[e.id_cantiere]&&em[e.id_cantiere]?(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("code",{className:"text-sm bg-green-50 text-green-700 px-2 py-1 rounded border border-green-200 font-mono",children:em[e.id_cantiere]}),(0,t.jsx)(o.$,{size:"sm",variant:"ghost",className:"h-7 w-7 p-0 text-green-600 hover:bg-green-50 hover:text-green-700 transition-colors",title:"Nascondi password",onClick:()=>ew(e),children:(0,t.jsx)(j.A,{className:"h-4 w-4"})})]}):(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,t.jsx)("span",{className:"text-sm text-gray-600 font-medium",children:"Configurata"})]}),(0,t.jsx)(o.$,{size:"sm",variant:"ghost",className:"h-7 w-7 p-0 text-blue-600 hover:bg-blue-50 hover:text-blue-700 transition-colors",title:"Mostra password",onClick:()=>ew(e),disabled:eo,children:eo?(0,t.jsx)(h.A,{className:"h-4 w-4 animate-spin"}):(0,t.jsx)(w.A,{className:"h-4 w-4"})})]})})})}),(0,t.jsx)(d.nA,{className:"py-4",children:(0,t.jsx)("div",{className:"flex items-center gap-2",children:M?(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(h.A,{className:"h-4 w-4 animate-spin text-gray-400"}),(0,t.jsx)("span",{className:"text-sm text-gray-500",children:"Caricamento..."})]}):(0,t.jsx)(t.Fragment,{children:(0,t.jsx)("div",{className:"flex-1 min-w-[120px]",children:(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3 shadow-inner",children:(0,t.jsx)("div",{className:"h-3 rounded-full transition-all duration-500 ease-out shadow-sm ".concat(((null==(a=$[e.id_cantiere])?void 0:a.percentuale_avanzamento)||0)>=90?"bg-gradient-to-r from-green-500 to-green-600":((null==(s=$[e.id_cantiere])?void 0:s.percentuale_avanzamento)||0)>=75?"bg-gradient-to-r from-blue-500 to-blue-600":((null==(r=$[e.id_cantiere])?void 0:r.percentuale_avanzamento)||0)>=50?"bg-gradient-to-r from-yellow-500 to-yellow-600":((null==(n=$[e.id_cantiere])?void 0:n.percentuale_avanzamento)||0)>=25?"bg-gradient-to-r from-orange-500 to-orange-600":"bg-gradient-to-r from-red-500 to-red-600"),style:{width:"".concat(Math.min((null==(i=$[e.id_cantiere])?void 0:i.percentuale_avanzamento)||0,100),"%")}})})})})})}),(0,t.jsx)(d.nA,{className:"py-4 text-center",children:M?(0,t.jsx)(h.A,{className:"h-4 w-4 animate-spin text-gray-400 mx-auto"}):(0,t.jsxs)("div",{className:"flex items-center justify-center gap-1",children:[(0,t.jsx)(N.A,{className:"h-4 w-4 text-gray-500"}),(0,t.jsxs)("span",{className:"text-sm font-semibold ".concat(((null==(l=$[e.id_cantiere])?void 0:l.percentuale_avanzamento)||0)>=90?"text-green-700":((null==(c=$[e.id_cantiere])?void 0:c.percentuale_avanzamento)||0)>=75?"text-blue-700":((null==(m=$[e.id_cantiere])?void 0:m.percentuale_avanzamento)||0)>=50?"text-yellow-700":((null==(x=$[e.id_cantiere])?void 0:x.percentuale_avanzamento)||0)>=25?"text-orange-700":"text-red-700"),children:[((null==(u=$[e.id_cantiere])?void 0:u.percentuale_avanzamento)||0).toFixed(1),"%"]})]})}),(0,t.jsx)(d.nA,{className:"text-center py-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,t.jsxs)(o.$,{size:"sm",variant:"outline",onClick:()=>eb(e),className:"h-9 px-3 text-gray-600 border-gray-200 hover:bg-gray-50 hover:text-gray-700 hover:border-gray-300 transition-all duration-200 ease-in-out",title:"Modifica dati cantiere",children:[(0,t.jsx)(y.A,{className:"h-4 w-4 mr-1.5"}),"Modifica"]}),(0,t.jsxs)(o.$,{size:"sm",onClick:()=>ef(e),className:"h-9 px-4 bg-blue-600 hover:bg-blue-700 text-white font-medium transition-all duration-200 ease-in-out shadow-sm hover:shadow-md hover:shadow-blue-500/25 transform hover:scale-105",title:"Accedi al cantiere",children:[(0,t.jsx)(_.A,{className:"h-4 w-4 mr-1.5"}),"Accedi"]})]})})]},e.id_cantiere)})})]})}),(0,t.jsx)(m.lG,{open:Z,onOpenChange:H,children:(0,t.jsxs)(m.Cf,{className:"sm:max-w-[425px]",children:[(0,t.jsxs)(m.c7,{children:[(0,t.jsx)(m.L3,{children:"Modifica Cantiere"}),(0,t.jsx)(m.rr,{children:"Modifica i dettagli del cantiere selezionato"})]}),(0,t.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(c.J,{htmlFor:"edit-commessa",className:"text-right",children:"Commessa"}),(0,t.jsx)(l.p,{id:"edit-commessa",value:Q.commessa,onChange:e=>Y({...Q,commessa:e.target.value}),className:"col-span-3"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(c.J,{htmlFor:"edit-descrizione",className:"text-right",children:"Descrizione"}),(0,t.jsx)(l.p,{id:"edit-descrizione",value:Q.descrizione,onChange:e=>Y({...Q,descrizione:e.target.value}),className:"col-span-3"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(c.J,{htmlFor:"edit-nome_cliente",className:"text-right",children:"Cliente"}),(0,t.jsx)(l.p,{id:"edit-nome_cliente",value:Q.nome_cliente,onChange:e=>Y({...Q,nome_cliente:e.target.value}),className:"col-span-3"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(c.J,{htmlFor:"edit-indirizzo_cantiere",className:"text-right",children:"Indirizzo"}),(0,t.jsx)(l.p,{id:"edit-indirizzo_cantiere",value:Q.indirizzo_cantiere,onChange:e=>Y({...Q,indirizzo_cantiere:e.target.value}),className:"col-span-3"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(c.J,{htmlFor:"edit-citta_cantiere",className:"text-right",children:"Citt\xe0"}),(0,t.jsx)(l.p,{id:"edit-citta_cantiere",value:Q.citta_cantiere,onChange:e=>Y({...Q,citta_cantiere:e.target.value}),className:"col-span-3"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(c.J,{htmlFor:"edit-nazione_cantiere",className:"text-right",children:"Nazione"}),(0,t.jsx)(l.p,{id:"edit-nazione_cantiere",value:Q.nazione_cantiere,onChange:e=>Y({...Q,nazione_cantiere:e.target.value}),className:"col-span-3"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(c.J,{className:"text-right",children:"Password"}),(0,t.jsx)("div",{className:"col-span-3",children:(0,t.jsxs)(o.$,{type:"button",variant:"outline",onClick:()=>{R(!0)},className:"w-full h-10 justify-start text-left bg-gray-50 hover:bg-gray-100 border-gray-200 hover:border-gray-300 transition-colors",children:[(0,t.jsx)(C.A,{className:"h-4 w-4 mr-2 text-gray-500"}),(0,t.jsx)("span",{className:"text-gray-700",children:"Modifica Password"})]})})]})]}),(0,t.jsxs)(m.Es,{children:[(0,t.jsx)(o.$,{onClick:()=>H(!1),className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:"Annulla"}),(0,t.jsx)(o.$,{onClick:ep,className:"relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:"Salva Modifiche"})]})]})}),(0,t.jsx)(m.lG,{open:X,onOpenChange:e=>{R(e),e||(ee({currentPassword:"",newPassword:"",confirmPassword:""}),J(""))},children:(0,t.jsxs)(m.Cf,{className:"sm:max-w-[600px]",children:[(0,t.jsxs)(m.c7,{children:[(0,t.jsxs)(m.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(C.A,{className:"h-5 w-5"}),"Gestione Password - ",null==W?void 0:W.commessa]}),(0,t.jsx)(m.rr,{children:"Modifica la password di accesso al cantiere"})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("h3",{className:"text-lg font-medium flex items-center gap-2",children:[(0,t.jsx)(z.A,{className:"h-5 w-5"}),"Cambia Password"]}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Inserisci la password attuale e la nuova password"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(c.J,{htmlFor:"current-password-change",children:"Password Attuale"}),(0,t.jsx)(l.p,{id:"current-password-change",type:"password",placeholder:"Password attuale per conferma",value:K.currentPassword,onChange:e=>ee({...K,currentPassword:e.target.value})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(c.J,{htmlFor:"new-password",children:"Nuova Password"}),(0,t.jsx)(l.p,{id:"new-password",type:"password",placeholder:"Inserisci la nuova password",value:K.newPassword,onChange:e=>ee({...K,newPassword:e.target.value})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(c.J,{htmlFor:"confirm-password",children:"Conferma Nuova Password"}),(0,t.jsx)(l.p,{id:"confirm-password",type:"password",placeholder:"Conferma la nuova password",value:K.confirmPassword,onChange:e=>ee({...K,confirmPassword:e.target.value})})]}),(0,t.jsxs)(o.$,{onClick:ev,disabled:E||!K.currentPassword||!K.newPassword||!K.confirmPassword,className:"w-full relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]",children:[E?(0,t.jsx)(h.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,t.jsx)(z.A,{className:"mr-2 h-4 w-4"}),"Cambia Password"]})]})]}),I&&(0,t.jsxs)("div",{className:"p-4 bg-red-50 border border-red-200 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(f.A,{className:"h-5 w-5 text-red-600"}),(0,t.jsx)("span",{className:"font-medium text-red-800",children:"Errore"})]}),(0,t.jsx)("p",{className:"text-sm text-red-700 mt-1",children:I})]})]}),(0,t.jsx)(m.Es,{children:(0,t.jsx)(o.$,{variant:"outline",onClick:()=>R(!1),children:"Chiudi"})})]})}),(0,t.jsx)(m.lG,{open:U,onOpenChange:e=>{V(e),e||(er(""),q(null),J(""))},children:(0,t.jsxs)(m.Cf,{className:"sm:max-w-[500px]",children:[(0,t.jsxs)(m.c7,{children:[(0,t.jsxs)(m.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(w.A,{className:"h-5 w-5 text-green-600"}),"Password Cantiere - ",null==W?void 0:W.commessa]}),(0,t.jsxs)(m.rr,{children:["Password per l'accesso al cantiere con codice: ",(0,t.jsx)("code",{className:"bg-muted px-2 py-1 rounded",children:null==W?void 0:W.codice_univoco})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[et&&(0,t.jsxs)("div",{className:"p-4 bg-green-50 border border-green-200 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,t.jsx)(A.A,{className:"h-5 w-5 text-green-600"}),(0,t.jsx)("span",{className:"font-medium text-green-800",children:"Password del Cantiere"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("code",{className:"flex-1 text-lg font-mono bg-white p-3 rounded border border-green-300 text-center",children:et}),(0,t.jsx)(o.$,{size:"sm",variant:"outline",onClick:()=>ej(et),className:"text-green-600 hover:bg-green-50 border-green-300",title:"Copia password",children:(0,t.jsx)(v.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("p",{className:"text-sm text-green-700 mt-2",children:["Utilizza questa password insieme al codice univoco ",(0,t.jsx)("strong",{children:null==W?void 0:W.codice_univoco})," per accedere al cantiere."]})]}),I&&(0,t.jsxs)("div",{className:"p-4 bg-red-50 border border-red-200 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(f.A,{className:"h-5 w-5 text-red-600"}),(0,t.jsx)("span",{className:"font-medium text-red-800",children:"Errore"})]}),(0,t.jsx)("p",{className:"text-sm text-red-700 mt-1",children:I})]})]}),(0,t.jsx)(m.Es,{children:(0,t.jsx)(o.$,{variant:"outline",onClick:()=>V(!1),children:"Chiudi"})})]})})]})}},54165:(e,a,s)=>{"use strict";s.d(a,{Cf:()=>m,Es:()=>u,L3:()=>h,c7:()=>x,lG:()=>o,rr:()=>g,zM:()=>l});var t=s(95155);s(12115);var r=s(15452),n=s(54416),i=s(59434);function o(e){let{...a}=e;return(0,t.jsx)(r.bL,{"data-slot":"dialog",...a})}function l(e){let{...a}=e;return(0,t.jsx)(r.l9,{"data-slot":"dialog-trigger",...a})}function c(e){let{...a}=e;return(0,t.jsx)(r.ZL,{"data-slot":"dialog-portal",...a})}function d(e){let{className:a,...s}=e;return(0,t.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...s})}function m(e){let{className:a,children:s,showCloseButton:o=!0,...l}=e;return(0,t.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,t.jsx)(d,{}),(0,t.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...l,children:[s,o&&(0,t.jsxs)(r.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,t.jsx)(n.A,{}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function x(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center sm:text-left",a),...s})}function u(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"dialog-footer",className:(0,i.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",a),...s})}function h(e){let{className:a,...s}=e;return(0,t.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",a),...s})}function g(e){let{className:a,...s}=e;return(0,t.jsx)(r.VY,{"data-slot":"dialog-description",className:(0,i.cn)("text-muted-foreground text-sm",a),...s})}},59434:(e,a,s)=>{"use strict";s.d(a,{cn:()=>n});var t=s(52596),r=s(39688);function n(){for(var e=arguments.length,a=Array(e),s=0;s<e;s++)a[s]=arguments[s];return(0,r.QP)((0,t.$)(a))}},62523:(e,a,s)=>{"use strict";s.d(a,{p:()=>n});var t=s(95155);s(12115);var r=s(59434);function n(e){let{className:a,type:s,...n}=e;return(0,t.jsx)("input",{type:s,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...n})}},66695:(e,a,s)=>{"use strict";s.d(a,{BT:()=>l,Wu:()=>c,ZB:()=>o,Zp:()=>n,aR:()=>i});var t=s(95155);s(12115);var r=s(59434);function n(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...s})}function i(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...s})}function o(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",a),...s})}function l(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",a),...s})}function c(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",a),...s})}},85057:(e,a,s)=>{"use strict";s.d(a,{J:()=>i});var t=s(95155);s(12115);var r=s(40968),n=s(59434);function i(e){let{className:a,...s}=e;return(0,t.jsx)(r.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...s})}},85127:(e,a,s)=>{"use strict";s.d(a,{A0:()=>i,BF:()=>o,Hj:()=>l,XI:()=>n,nA:()=>d,nd:()=>c});var t=s(95155);s(12115);var r=s(59434);function n(e){let{className:a,...s}=e;return(0,t.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,t.jsx)("table",{"data-slot":"table",className:(0,r.cn)("w-full caption-bottom text-sm border-collapse",a),...s})})}function i(e){let{className:a,...s}=e;return(0,t.jsx)("thead",{"data-slot":"table-header",className:(0,r.cn)("[&_tr]:border-b",a),...s})}function o(e){let{className:a,...s}=e;return(0,t.jsx)("tbody",{"data-slot":"table-body",className:(0,r.cn)("[&_tr:last-child]:border-0",a),...s})}function l(e){let{className:a,...s}=e;return(0,t.jsx)("tr",{"data-slot":"table-row",className:(0,r.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",a),...s})}function c(e){let{className:a,...s}=e;return(0,t.jsx)("th",{"data-slot":"table-head",className:(0,r.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...s})}function d(e){let{className:a,...s}=e;return(0,t.jsx)("td",{"data-slot":"table-cell",className:(0,r.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...s})}}},e=>{var a=a=>e(e.s=a);e.O(0,[3455,3464,8346,6481,283,8441,1684,7358],()=>a(40321)),_N_E=e.O()}]);