'use client'

import React, { useState, useRef, useEffect } from 'react'
import { createPortal } from 'react-dom'

interface TooltipProps {
  content: string | React.ReactNode
  children: React.ReactNode
  position?: 'top' | 'bottom' | 'left' | 'right' | 'auto'
  delay?: number
  className?: string
  disabled?: boolean
  maxWidth?: number
}

interface TooltipPosition {
  top: number
  left: number
  position: 'top' | 'bottom' | 'left' | 'right'
}

export const CableTooltip: React.FC<TooltipProps> = ({
  content,
  children,
  position = 'auto',
  delay = 500,
  className = '',
  disabled = false,
  maxWidth = 250
}) => {
  const [isVisible, setIsVisible] = useState(false)
  const [tooltipPosition, setTooltipPosition] = useState<TooltipPosition | null>(null)
  const triggerRef = useRef<HTMLDivElement>(null)
  const tooltipRef = useRef<HTMLDivElement>(null)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)

  const calculatePosition = (): TooltipPosition | null => {
    if (!triggerRef.current) return null

    const triggerRect = triggerRef.current.getBoundingClientRect()
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight
    const tooltipWidth = maxWidth
    const tooltipHeight = 40 // Estimated height

    let finalPosition = position
    let top = 0
    let left = 0

    // Auto positioning logic
    if (position === 'auto') {
      const spaceTop = triggerRect.top
      const spaceBottom = viewportHeight - triggerRect.bottom
      const spaceLeft = triggerRect.left
      const spaceRight = viewportWidth - triggerRect.right

      if (spaceTop > tooltipHeight && spaceTop > spaceBottom) {
        finalPosition = 'top'
      } else if (spaceBottom > tooltipHeight) {
        finalPosition = 'bottom'
      } else if (spaceRight > tooltipWidth) {
        finalPosition = 'right'
      } else {
        finalPosition = 'left'
      }
    }

    // Calculate position based on final position
    switch (finalPosition) {
      case 'top':
        top = triggerRect.top - tooltipHeight - 8
        left = triggerRect.left + (triggerRect.width / 2) - (tooltipWidth / 2)
        break
      case 'bottom':
        top = triggerRect.bottom + 8
        left = triggerRect.left + (triggerRect.width / 2) - (tooltipWidth / 2)
        break
      case 'left':
        top = triggerRect.top + (triggerRect.height / 2) - (tooltipHeight / 2)
        left = triggerRect.left - tooltipWidth - 8
        break
      case 'right':
        top = triggerRect.top + (triggerRect.height / 2) - (tooltipHeight / 2)
        left = triggerRect.right + 8
        break
    }

    // Ensure tooltip stays within viewport
    left = Math.max(8, Math.min(left, viewportWidth - tooltipWidth - 8))
    top = Math.max(8, Math.min(top, viewportHeight - tooltipHeight - 8))

    return { top, left, position: finalPosition }
  }

  const showTooltip = () => {
    if (disabled) return

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }

    timeoutRef.current = setTimeout(() => {
      const position = calculatePosition()
      if (position) {
        setTooltipPosition(position)
        setIsVisible(true)
      }
    }, delay)
  }

  const hideTooltip = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
      timeoutRef.current = null
    }
    setIsVisible(false)
    setTooltipPosition(null)
  }

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  const getArrowClasses = (pos: string) => {
    const baseClasses = "absolute w-0 h-0 border-solid"
    switch (pos) {
      case 'top':
        return `${baseClasses} top-full left-1/2 transform -translate-x-1/2 border-l-4 border-r-4 border-t-4 border-l-transparent border-r-transparent border-t-gray-900`
      case 'bottom':
        return `${baseClasses} bottom-full left-1/2 transform -translate-x-1/2 border-l-4 border-r-4 border-b-4 border-l-transparent border-r-transparent border-b-gray-900`
      case 'left':
        return `${baseClasses} left-full top-1/2 transform -translate-y-1/2 border-t-4 border-b-4 border-l-4 border-t-transparent border-b-transparent border-l-gray-900`
      case 'right':
        return `${baseClasses} right-full top-1/2 transform -translate-y-1/2 border-t-4 border-b-4 border-r-4 border-t-transparent border-b-transparent border-r-gray-900`
      default:
        return baseClasses
    }
  }

  const tooltipElement = isVisible && tooltipPosition ? (
    <div
      ref={tooltipRef}
      className={`fixed z-50 px-3 py-2 text-sm text-white bg-gray-900 rounded-md shadow-lg pointer-events-none transition-opacity duration-200 ${className}`}
      style={{
        top: tooltipPosition.top,
        left: tooltipPosition.left,
        maxWidth: maxWidth,
        wordWrap: 'break-word',
        whiteSpace: 'normal'
      }}
      role="tooltip"
      aria-hidden={!isVisible}
    >
      {content}
      <div className={getArrowClasses(tooltipPosition.position)} />
    </div>
  ) : null

  return (
    <>
      <div
        ref={triggerRef}
        onMouseEnter={showTooltip}
        onMouseLeave={hideTooltip}
        onFocus={showTooltip}
        onBlur={hideTooltip}
        className="inline-block"
        aria-describedby={isVisible ? 'tooltip' : undefined}
      >
        {children}
      </div>
      {typeof document !== 'undefined' && tooltipElement && createPortal(tooltipElement, document.body)}
    </>
  )
}

// Specialized tooltips for different cable actions
interface ActionTooltipProps {
  action: 'connect' | 'disconnect' | 'certify' | 'generate_pdf' | 'install' | 'modify'
  cableId?: string
  disabled?: boolean
  children: React.ReactNode
}

export const ActionTooltip: React.FC<ActionTooltipProps> = ({
  action,
  cableId,
  disabled = false,
  children
}) => {
  const getTooltipContent = () => {
    if (disabled) {
      switch (action) {
        case 'connect':
          return 'Collegamento disponibile solo per cavi installati'
        case 'disconnect':
          return 'Nessun collegamento da scollegare'
        case 'certify':
          return 'Certificazione disponibile solo per cavi installati e collegati'
        case 'generate_pdf':
          return 'Generazione PDF disponibile solo per cavi certificati'
        default:
          return 'Azione non disponibile'
      }
    }

    switch (action) {
      case 'connect':
        return `Collega il cavo ${cableId || ''} alle sue destinazioni`
      case 'disconnect':
        return `Scollega il cavo ${cableId || ''} da tutte le connessioni`
      case 'certify':
        return `Certifica il cavo ${cableId || ''} dopo i test di collaudo`
      case 'generate_pdf':
        return `Genera certificato PDF per il cavo ${cableId || ''}`
      case 'install':
        return `Inserisci metri installati per il cavo ${cableId || ''}`
      case 'modify':
        return `Modifica i dati della bobina per il cavo ${cableId || ''}`
      default:
        return 'Azione disponibile'
    }
  }

  return (
    <CableTooltip
      content={getTooltipContent()}
      disabled={false}
      delay={300}
      position="auto"
    >
      {children}
    </CableTooltip>
  )
}

// KPI Tooltip for statistics
interface KpiTooltipProps {
  type: 'total' | 'installed' | 'in_progress' | 'to_install' | 'connected' | 'certified'
  count: number
  percentage?: number
  children: React.ReactNode
}

export const KpiTooltip: React.FC<KpiTooltipProps> = ({
  type,
  count,
  percentage,
  children
}) => {
  const getTooltipContent = () => {
    const baseText = `${count} cavi`
    const percentageText = percentage !== undefined ? ` (${percentage.toFixed(1)}%)` : ''
    
    switch (type) {
      case 'total':
        return `Totale cavi nel progetto: ${baseText}`
      case 'installed':
        return `Cavi fisicamente installati: ${baseText}${percentageText}`
      case 'in_progress':
        return `Cavi in corso di installazione: ${baseText}${percentageText}`
      case 'to_install':
        return `Cavi ancora da installare: ${baseText}${percentageText}`
      case 'connected':
        return `Cavi completamente collegati: ${baseText}${percentageText}`
      case 'certified':
        return `Cavi certificati e collaudati: ${baseText}${percentageText}`
      default:
        return baseText
    }
  }

  return (
    <CableTooltip
      content={
        <div>
          <div className="font-medium">{getTooltipContent()}</div>
          <div className="text-xs text-gray-300 mt-1">
            Clicca per filtrare la tabella
          </div>
        </div>
      }
      delay={200}
      position="bottom"
    >
      {children}
    </CableTooltip>
  )
}
