"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[283],{25731:(e,t,a)=>{a.d(t,{AR:()=>s,At:()=>i,CV:()=>l,FH:()=>n,Fw:()=>r,ZQ:()=>c,_I:()=>g,dG:()=>m,km:()=>p,mg:()=>d,ug:()=>u});let o=a(23464).A.create({baseURL:"http://localhost:8001",timeout:3e4,headers:{"Content-Type":"application/json"}});o.interceptors.request.use(e=>{{let t=localStorage.getItem("token");t&&(e.headers.Authorization="Bearer ".concat(t))}return e},e=>Promise.reject(e)),o.interceptors.response.use(e=>e,e=>{var t;return(null==(t=e.response)?void 0:t.status)===401&&(localStorage.removeItem("token"),localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),localStorage.removeItem("cantiere_data"),window.location.href="/login"),Promise.reject(e)});let n={get:async(e,t)=>(await o.get(e,t)).data,post:async(e,t,a)=>(await o.post(e,t,a)).data,put:async(e,t,a)=>(await o.put(e,t,a)).data,patch:async(e,t,a)=>(await o.patch(e,t,a)).data,delete:async(e,t)=>(await o.delete(e,t)).data},c={login:async e=>{let t=new FormData;return t.append("username",e.username),t.append("password",e.password),(await o.post("/api/auth/login",t,{headers:{"Content-Type":"application/x-www-form-urlencoded"}})).data},loginCantiere:e=>n.post("/api/auth/login/cantiere",{codice_univoco:e.codice_cantiere,password:e.password_cantiere}),verifyToken:()=>n.post("/api/auth/test-token"),logout:()=>{localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),window.location.href="/login"}},i={getCavi:(e,t)=>n.get("/api/cavi/".concat(e),{params:t}),getCavo:(e,t)=>n.get("/api/cavi/".concat(e,"/").concat(t)),checkCavo:(e,t)=>n.get("/api/cavi/".concat(e,"/check/").concat(t)),createCavo:(e,t)=>n.post("/api/cavi/".concat(e),t),updateCavo:(e,t,a)=>n.put("/api/cavi/".concat(e,"/").concat(t),a),deleteCavo:(e,t,a)=>n.delete("/api/cavi/".concat(e,"/").concat(t),{data:a}),updateMetriPosati:(e,t,a,o,c)=>n.post("/api/cavi/".concat(e,"/").concat(t,"/metri-posati"),{metri_posati:a,id_bobina:o,force_over:c||!1}),updateBobina:(e,t,a,o)=>n.post("/api/cavi/".concat(e,"/").concat(t,"/bobina"),{id_bobina:a,force_over:o||!1}),cancelInstallation:(e,t)=>n.post("/api/cavi/".concat(e,"/").concat(t,"/cancel-installation")),collegaCavo:(e,t,a,o)=>n.post("/api/cavi/".concat(e,"/").concat(t,"/collegamento"),{lato:a,responsabile:o}),scollegaCavo:(e,t,a)=>n.delete("/api/cavi/".concat(e,"/").concat(t,"/collegamento"),{data:{lato:a}}),markAsSpare:(e,t,a)=>n.put("/api/cavi/".concat(e,"/").concat(t,"/spare"),{spare:+!!a}),debugCavi:e=>n.get("/api/cavi/debug/".concat(e)),debugCaviRaw:e=>n.get("/api/cavi/debug/raw/".concat(e))},r={getBobine:(e,t)=>n.get("/api/parco-cavi/".concat(e),{params:t}),getBobina:(e,t)=>n.get("/api/parco-cavi/".concat(e,"/").concat(t)),getBobineCompatibili:(e,t)=>n.get("/api/parco-cavi/".concat(e,"/compatibili"),{params:t}),createBobina:(e,t)=>n.post("/api/parco-cavi/".concat(e),t),updateBobina:(e,t,a)=>n.put("/api/parco-cavi/".concat(e,"/").concat(t),a),deleteBobina:(e,t)=>n.delete("/api/parco-cavi/".concat(e,"/").concat(t)),isFirstBobinaInsertion:e=>n.get("/api/parco-cavi/".concat(e,"/is-first-insertion")),updateBobina:(e,t,a)=>n.put("/api/parco-cavi/".concat(e,"/").concat(t),a),deleteBobina:(e,t)=>n.delete("/api/parco-cavi/".concat(e,"/").concat(t)),checkDisponibilita:(e,t,a)=>n.get("/api/parco-cavi/".concat(e,"/").concat(t,"/disponibilita"),{params:{metri_richiesti:a}})},l={getComande:e=>n.get("/api/comande/cantiere/".concat(e)),getComanda:(e,t)=>n.get("/api/comande/".concat(t)),getCaviComanda:e=>n.get("/api/comande/".concat(e,"/cavi")),createComanda:(e,t)=>n.post("/api/comande/cantiere/".concat(e),t),createComandaWithCavi:(e,t,a)=>n.post("/api/comande/cantiere/".concat(e,"/crea-con-cavi"),t,{params:{lista_id_cavi:a}}),updateDatiComanda:(e,t,a)=>n.put("/api/comande/".concat(e,"/").concat(t),a),updateComanda:(e,t,a)=>n.put("/api/comande/cantiere/".concat(e,"/").concat(t),a),deleteComanda:(e,t)=>n.delete("/api/comande/cantiere/".concat(e,"/").concat(t)),assegnaCavi:(e,t,a)=>n.post("/api/comande/cantiere/".concat(e,"/").concat(t,"/assegna-cavi"),{cavi_ids:a}),rimuoviCavi:(e,t,a)=>n.delete("/api/comande/cantiere/".concat(e,"/").concat(t,"/rimuovi-cavi"),{data:{cavi_ids:a}}),getStatistiche:e=>n.get("/api/comande/cantiere/".concat(e,"/statistiche")),cambiaStato:(e,t,a)=>n.put("/api/comande/cantiere/".concat(e,"/").concat(t,"/stato"),{nuovo_stato:a})},s={getResponsabili:e=>n.get("/api/responsabili/cantiere/".concat(e)),createResponsabile:(e,t)=>n.post("/api/responsabili/".concat(e),t),updateResponsabile:(e,t,a)=>n.put("/api/responsabili/".concat(e,"/").concat(t),a),deleteResponsabile:(e,t)=>n.delete("/api/responsabili/".concat(e,"/").concat(t))},p={getCertificazioni:e=>n.get("/api/cantieri/".concat(e,"/certificazioni")),createCertificazione:(e,t)=>n.post("/api/cantieri/".concat(e,"/certificazioni"),t),generatePDF:(e,t)=>n.get("/api/cantieri/".concat(e,"/pdf-cei-64-8/").concat(t),{responseType:"blob"})},d={importCavi:(e,t,a)=>{let o=new FormData;return o.append("file",t),o.append("revisione",a),n.post("/api/excel/".concat(e,"/import-cavi"),o,{headers:{"Content-Type":"multipart/form-data"}})},importBobine:(e,t)=>{let a=new FormData;return a.append("file",t),n.post("/api/excel/".concat(e,"/import-parco-bobine"),a,{headers:{"Content-Type":"multipart/form-data"}})},exportCavi:e=>n.get("/api/excel/".concat(e,"/export-cavi"),{responseType:"blob"}),exportBobine:e=>n.get("/api/excel/".concat(e,"/export-parco-bobine"),{responseType:"blob"})},u={getReportAvanzamento:e=>n.get("/api/reports/".concat(e,"/avanzamento")),getReportBOQ:e=>n.get("/api/reports/".concat(e,"/boq")),getReportUtilizzoBobine:e=>n.get("/api/reports/".concat(e,"/storico-bobine")),getReportProgress:e=>n.get("/api/reports/".concat(e,"/progress")),getReportPosaPeriodo:(e,t,a)=>{let o=new URLSearchParams;t&&o.append("data_inizio",t),a&&o.append("data_fine",a);let c=o.toString();return n.get("/api/reports/".concat(e,"/posa-periodo").concat(c?"?".concat(c):""))}},g={getCantieri:()=>n.get("/api/cantieri"),getCantiere:e=>n.get("/api/cantieri/".concat(e)),createCantiere:e=>n.post("/api/cantieri",e),updateCantiere:(e,t)=>n.put("/api/cantieri/".concat(e),t),getCantiereStatistics:e=>n.get("/api/cantieri/".concat(e,"/statistics"))},m={getUsers:()=>n.get("/api/users"),getUser:e=>n.get("/api/users/".concat(e)),createUser:e=>n.post("/api/users",e),updateUser:(e,t)=>n.put("/api/users/".concat(e),t),deleteUser:e=>n.delete("/api/users/".concat(e)),toggleUserStatus:e=>n.get("/api/users/toggle/".concat(e)),checkExpiredUsers:()=>n.get("/api/users/check-expired"),impersonateUser:e=>n.post("/api/auth/impersonate",{user_id:e}),getDatabaseData:()=>n.get("/api/users/db-raw"),resetDatabase:()=>n.post("/api/admin/reset-database")}},40283:(e,t,a)=>{a.d(t,{A:()=>r,AuthProvider:()=>l});var o=a(95155),n=a(12115),c=a(25731);let i=(0,n.createContext)(void 0);function r(){let e=(0,n.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function l(e){let{children:t}=e,[a,r]=(0,n.useState)(null),[l,s]=(0,n.useState)(null),[p,d]=(0,n.useState)(!0),[u,g]=(0,n.useState)(()=>"true"===localStorage.getItem("isImpersonating")),[m,v]=(0,n.useState)(()=>{{let e=localStorage.getItem("impersonatedUser");return e?JSON.parse(e):null}}),[C,_]=(0,n.useState)(null),[S,h]=(0,n.useState)(null),[I,b]=(0,n.useState)(null),x=!!a||!!l;console.log("\uD83D\uDD10 AuthContext: Stato corrente:",{user:a?{id:a.id_utente,username:a.username,ruolo:a.ruolo}:null,cantiere:l?{id:l.id_cantiere}:null,isAuthenticated:x,isLoading:p}),(0,n.useEffect)(()=>{w()},[]),(0,n.useEffect)(()=>{if(a&&!p&&!l){let e=localStorage.getItem("selectedCantiereId"),t=localStorage.getItem("selectedCantiereName");if(e&&"null"!==e&&"undefined"!==e){let o=parseInt(e,10);if(!isNaN(o)&&o>0){let e={id_cantiere:o,commessa:t||"Cantiere ".concat(o),codice_univoco:"",id_utente:a.id_utente};console.log("\uD83C\uDFD7️ AuthContext: Caricamento cantiere dal localStorage:",e),s(e)}else console.warn("\uD83C\uDFD7️ AuthContext: ID cantiere non valido nel localStorage:",e),localStorage.removeItem("selectedCantiereId"),localStorage.removeItem("selectedCantiereName")}}},[a,p,l]);let w=async()=>{try{if(d(!0),localStorage.getItem("token"))try{let e=await c.ZQ.verifyToken(),t={id_utente:e.user_id,username:e.username,ruolo:e.role};r(t);let a=!0===e.is_impersonated;if(g(a),a&&e.impersonated_id){let t={id:e.impersonated_id,username:e.impersonated_username,role:e.impersonated_role};v(t),localStorage.setItem("impersonatedUser",JSON.stringify(t)),localStorage.setItem("isImpersonating","true")}else v(null),localStorage.removeItem("impersonatedUser"),localStorage.removeItem("isImpersonating");if("cantieri_user"===e.role&&e.cantiere_id){let t={id_cantiere:e.cantiere_id,commessa:e.cantiere_name||"Cantiere ".concat(e.cantiere_id),codice_univoco:"",id_utente:e.user_id};console.log("\uD83C\uDFD7️ AuthContext: Impostazione cantiere per utente cantiere:",t),s(t),localStorage.setItem("selectedCantiereId",e.cantiere_id.toString()),localStorage.setItem("selectedCantiereName",t.commessa)}else{console.log("\uD83C\uDFD7️ AuthContext: Utente standard, controllo cantiere dal localStorage");let e=localStorage.getItem("cantiere_data");if(e)try{let t=JSON.parse(e);console.log("\uD83C\uDFD7️ AuthContext: Caricamento cantiere da cantiere_data:",t),s(t),localStorage.setItem("selectedCantiereId",t.id_cantiere.toString()),localStorage.setItem("selectedCantiereName",t.commessa)}catch(e){console.warn("\uD83C\uDFD7️ AuthContext: Errore parsing cantiere_data:",e),localStorage.removeItem("cantiere_data")}}}catch(e){localStorage.removeItem("token"),localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),localStorage.removeItem("cantiere_data"),r(null),s(null)}else r(null),s(null)}catch(e){localStorage.removeItem("token"),localStorage.removeItem("access_token"),localStorage.removeItem("user_data"),localStorage.removeItem("cantiere_data"),r(null),s(null)}finally{setTimeout(()=>{d(!1)},500)}},f=async(e,t)=>{try{console.log("\uD83D\uDD10 AuthContext: Inizio login per:",e),d(!0);let a=await c.ZQ.login({username:e,password:t});console.log("\uD83D\uDCE1 AuthContext: Risposta backend ricevuta:",a);{localStorage.setItem("token",a.access_token),console.log("\uD83D\uDCBE AuthContext: Token salvato nel localStorage");let e={id_utente:a.user_id,username:a.username,ruolo:a.role};return a.expiration_warning?(console.log("⚠️ AuthContext: Warning scadenza ricevuto:",a.expiration_warning),_(a.expiration_warning),h(a.days_until_expiration),b(a.expiration_date)):(_(null),h(null),b(null)),console.log("\uD83D\uDC64 AuthContext: Dati utente creati:",e),r(e),s(null),console.log("✅ AuthContext: Stato utente aggiornato, restituisco userData"),e}}catch(e){throw console.error("❌ AuthContext: Errore durante login:",e),e}finally{d(!1)}},A=async(e,t)=>{try{console.log("\uD83D\uDD10 AuthContext: Inizio login cantiere:",e),d(!0);let a=await c.ZQ.loginCantiere({codice_cantiere:e,password_cantiere:t});console.log("\uD83D\uDD10 AuthContext: Risposta login cantiere:",a);{localStorage.setItem("token",a.access_token),console.log("\uD83D\uDD10 AuthContext: Token salvato");let t={id_cantiere:a.cantiere_id,commessa:a.cantiere_name,codice_univoco:e,id_utente:a.user_id};return console.log("\uD83D\uDD10 AuthContext: Dati cantiere preparati:",t),localStorage.setItem("cantiere_data",JSON.stringify(t)),console.log("\uD83D\uDD10 AuthContext: Dati cantiere salvati in localStorage"),s(t),r(null),console.log("\uD83D\uDD10 AuthContext: Context aggiornato"),await w(),console.log("\uD83D\uDD10 AuthContext: checkAuth completato"),t}}catch(e){throw e}finally{d(!1)}},k=async e=>{try{let t=await c.dG.impersonateUser(e);{localStorage.setItem("token",t.access_token);let e={id:t.impersonated_id,username:t.impersonated_username,role:t.impersonated_role};return localStorage.setItem("impersonatedUser",JSON.stringify(e)),v(e),g(!0),localStorage.setItem("isImpersonating","true"),{impersonatedUser:e}}}catch(e){throw e}};return(0,o.jsx)(i.Provider,{value:{user:a,cantiere:l,isAuthenticated:x,isLoading:p,isImpersonating:u,impersonatedUser:m,expirationWarning:C,daysUntilExpiration:S,expirationDate:I,login:f,loginCantiere:A,logout:()=>{localStorage.clear(),sessionStorage.clear(),r(null),s(null),g(!1),v(null),_(null),h(null),b(null),window.location.replace("/login")},checkAuth:w,impersonateUser:k,selectCantiere:e=>{if(!e||!e.id_cantiere||e.id_cantiere<=0)return void console.error("\uD83C\uDFD7️ AuthContext: Tentativo di selezione cantiere non valido:",e);try{let t=e.commessa||"Cantiere ".concat(e.id_cantiere);localStorage.setItem("selectedCantiereId",e.id_cantiere.toString()),localStorage.setItem("selectedCantiereName",t);let a={...e,commessa:t};console.log("\uD83C\uDFD7️ AuthContext: Cantiere selezionato:",a),s(a),localStorage.removeItem("cantiere_data")}catch(e){console.error("\uD83C\uDFD7️ AuthContext: Errore nella selezione cantiere:",e)}},clearCantiere:()=>{console.log("\uD83C\uDFD7️ AuthContext: Pulizia stato cantiere"),s(null),localStorage.removeItem("selectedCantiereId"),localStorage.removeItem("selectedCantiereName"),localStorage.removeItem("cantiere_data")},dismissExpirationWarning:()=>{_(null),h(null),b(null)}},children:t})}}}]);