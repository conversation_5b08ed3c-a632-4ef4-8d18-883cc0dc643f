'use client'

import React, { useState } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON>alogFooter,
  <PERSON>alogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  AlertTriangle, 
  FileText, 
  X, 
  Download,
  AlertCircle,
  CheckCircle,
  Loader2
} from 'lucide-react'
import { Cavo } from '@/types'

// Types for modal props
interface BaseModalProps {
  open: boolean
  onClose: () => void
  cavo: Cavo | null
}

interface DisconnectModalProps extends BaseModalProps {
  onConfirm: (cavoId: string) => Promise<void>
}

interface GeneratePdfModalProps extends BaseModalProps {
  onGenerate: (cavoId: string, options: PdfGenerationOptions) => Promise<void>
}

interface CertificationErrorModalProps extends BaseModalProps {
  errorMessage?: string
  missingRequirements?: string[]
}

interface PdfGenerationOptions {
  fileName: string
  includeTestData: boolean
  format: 'standard' | 'detailed'
  emailRecipient?: string
}

// Disconnect Confirmation Modal
export const DisconnectCableModal: React.FC<DisconnectModalProps> = ({
  open,
  onClose,
  cavo,
  onConfirm
}) => {
  const [isLoading, setIsLoading] = useState(false)

  const handleConfirm = async () => {
    if (!cavo) return
    
    setIsLoading(true)
    try {
      await onConfirm(cavo.id_cavo)
      onClose()
    } catch (error) {
      console.error('Error disconnecting cable:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose()
    }
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent 
        className="sm:max-w-md"
        onKeyDown={handleKeyDown}
        aria-labelledby="disconnect-modal-title"
        aria-describedby="disconnect-modal-description"
      >
        <DialogHeader>
          <DialogTitle id="disconnect-modal-title" className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-orange-500" />
            Conferma Scollegamento Cavo
          </DialogTitle>
          <DialogDescription id="disconnect-modal-description">
            Sei sicuro di voler scollegare il cavo <strong>{cavo?.id_cavo}</strong>?
            <br />
            <span className="text-sm text-muted-foreground mt-2 block">
              Questa azione potrebbe influenzare lo stato di altri componenti collegati.
            </span>
          </DialogDescription>
        </DialogHeader>

        <Alert className="my-4">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Lo scollegamento rimuoverà tutte le connessioni attive del cavo.
            Assicurati che questa operazione sia necessaria.
          </AlertDescription>
        </Alert>

        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isLoading}
            className="flex-1"
          >
            Annulla
          </Button>
          <Button
            variant="destructive"
            onClick={handleConfirm}
            disabled={isLoading}
            className="flex-1"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Scollegando...
              </>
            ) : (
              'Scollega'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

// PDF Generation Modal
export const GeneratePdfModal: React.FC<GeneratePdfModalProps> = ({
  open,
  onClose,
  cavo,
  onGenerate
}) => {
  const [isLoading, setIsLoading] = useState(false)
  const [options, setOptions] = useState<PdfGenerationOptions>({
    fileName: '',
    includeTestData: true,
    format: 'standard',
    emailRecipient: ''
  })

  // Set default filename when modal opens
  React.useEffect(() => {
    if (cavo && open) {
      setOptions(prev => ({
        ...prev,
        fileName: `Certificato_${cavo.id_cavo}_${new Date().toISOString().split('T')[0]}.pdf`
      }))
    }
  }, [cavo, open])

  const handleGenerate = async () => {
    if (!cavo) return
    
    setIsLoading(true)
    try {
      await onGenerate(cavo.id_cavo, options)
      onClose()
    } catch (error) {
      console.error('Error generating PDF:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose()
    }
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent 
        className="sm:max-w-lg"
        onKeyDown={handleKeyDown}
        aria-labelledby="pdf-modal-title"
      >
        <DialogHeader>
          <DialogTitle id="pdf-modal-title" className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-blue-500" />
            Genera Certificato per Cavo {cavo?.id_cavo}
          </DialogTitle>
          <DialogDescription>
            Configura le opzioni per la generazione del certificato PDF
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {/* File Name */}
          <div className="space-y-2">
            <Label htmlFor="fileName">Nome File</Label>
            <Input
              id="fileName"
              value={options.fileName}
              onChange={(e) => setOptions(prev => ({ ...prev, fileName: e.target.value }))}
              placeholder="Inserisci il nome del file"
            />
          </div>

          {/* Format Selection */}
          <div className="space-y-2">
            <Label>Formato Certificato</Label>
            <div className="flex gap-4">
              <label className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="radio"
                  name="format"
                  value="standard"
                  checked={options.format === 'standard'}
                  onChange={(e) => setOptions(prev => ({ ...prev, format: e.target.value as 'standard' | 'detailed' }))}
                  className="text-blue-600"
                />
                <span className="text-sm">Standard</span>
              </label>
              <label className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="radio"
                  name="format"
                  value="detailed"
                  checked={options.format === 'detailed'}
                  onChange={(e) => setOptions(prev => ({ ...prev, format: e.target.value as 'standard' | 'detailed' }))}
                  className="text-blue-600"
                />
                <span className="text-sm">Dettagliato</span>
              </label>
            </div>
          </div>

          {/* Include Test Data */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="includeTestData"
              checked={options.includeTestData}
              onCheckedChange={(checked) => setOptions(prev => ({ ...prev, includeTestData: checked as boolean }))}
            />
            <Label htmlFor="includeTestData" className="text-sm">
              Includi Dati di Collaudo
            </Label>
          </div>

          {/* Email Recipient (Optional) */}
          <div className="space-y-2">
            <Label htmlFor="emailRecipient">Email Destinatario (Opzionale)</Label>
            <Input
              id="emailRecipient"
              type="email"
              value={options.emailRecipient}
              onChange={(e) => setOptions(prev => ({ ...prev, emailRecipient: e.target.value }))}
              placeholder="<EMAIL>"
            />
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isLoading}
            className="flex-1"
          >
            Annulla
          </Button>
          <Button
            onClick={handleGenerate}
            disabled={isLoading || !options.fileName.trim()}
            className="flex-1"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Generando...
              </>
            ) : (
              <>
                <Download className="mr-2 h-4 w-4" />
                Genera PDF
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

// Certification Error Modal
export const CertificationErrorModal: React.FC<CertificationErrorModalProps> = ({
  open,
  onClose,
  cavo,
  errorMessage,
  missingRequirements = []
}) => {
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose()
    }
  }

  const defaultRequirements = [
    'Il cavo deve essere nello stato "Installato"',
    'Il cavo deve essere completamente collegato',
    'Tutti i dati di collaudo devono essere presenti'
  ]

  const requirements = missingRequirements.length > 0 ? missingRequirements : defaultRequirements

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent
        className="sm:max-w-md"
        onKeyDown={handleKeyDown}
        aria-labelledby="certification-error-title"
      >
        <DialogHeader>
          <DialogTitle id="certification-error-title" className="flex items-center gap-2">
            <X className="h-5 w-5 text-red-500" />
            Impossibile Certificare Cavo
          </DialogTitle>
          <DialogDescription>
            Il cavo <strong>{cavo?.id_cavo}</strong> non può essere certificato in questo stato.
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          {errorMessage && (
            <Alert className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{errorMessage}</AlertDescription>
            </Alert>
          )}

          <div className="space-y-3">
            <h4 className="text-sm font-medium text-gray-900">
              Requisiti mancanti:
            </h4>
            <ul className="space-y-2">
              {requirements.map((requirement, index) => (
                <li key={index} className="flex items-start gap-2 text-sm text-gray-600">
                  <X className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
                  <span>{requirement}</span>
                </li>
              ))}
            </ul>
          </div>

          <Alert className="mt-4">
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              Completa tutti i requisiti sopra elencati per abilitare la certificazione.
            </AlertDescription>
          </Alert>
        </div>

        <DialogFooter>
          <Button onClick={onClose} className="w-full">
            Ho Capito
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

// Success Toast Component (for feedback after actions)
interface SuccessToastProps {
  message: string
  visible: boolean
  onClose: () => void
}

export const SuccessToast: React.FC<SuccessToastProps> = ({
  message,
  visible,
  onClose
}) => {
  React.useEffect(() => {
    if (visible) {
      const timer = setTimeout(() => {
        onClose()
      }, 3000)
      return () => clearTimeout(timer)
    }
  }, [visible, onClose])

  if (!visible) return null

  return (
    <div className="fixed top-4 right-4 z-50 animate-in slide-in-from-top-2">
      <Alert className="bg-green-50 border-green-200 text-green-800 shadow-lg">
        <CheckCircle className="h-4 w-4" />
        <AlertDescription className="font-medium">
          {message}
        </AlertDescription>
        <Button
          variant="ghost"
          size="sm"
          className="absolute top-2 right-2 h-6 w-6 p-0"
          onClick={onClose}
        >
          <X className="h-3 w-3" />
        </Button>
      </Alert>
    </div>
  )
}

// Export all modal types for easy importing
export type {
  DisconnectModalProps,
  GeneratePdfModalProps,
  CertificationErrorModalProps,
  PdfGenerationOptions
}
